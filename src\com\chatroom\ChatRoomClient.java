package com.chatroom;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import javax.swing.text.BadLocationException;
import javax.swing.text.SimpleAttributeSet;
import javax.swing.text.StyleConstants;
import javax.swing.text.StyledDocument;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.net.Socket;
import java.util.List;
import java.util.Objects;

/**
 * 聊天室客户端 - 包含所有客户端相关功能
 *
 */
public class ChatRoomClient {

    // 用户登录窗口
    public static class LoginFrame extends J<PERSON>rame {
        // 界面组件
        private JTextField usernameField;
        private JPasswordField passwordField;
        private JButton LoginButton;
        private JButton ExitButton;
        private JPanel loginPanel;
        private JTextField serverAddressField;  // 服务器地址输入
        private JTextField portField;  // 端口号输入
        private JLabel usernameLabel;
        private JLabel passwordLabel;
        private JLabel serverAddressLabel;
        private JLabel portLabel;
        private JButton testButton;  // 连接测试按钮
        private JLabel picLabel;

        public LoginFrame(Client client) {
            super("简易聊天室 - 登录");
            initComponents();
            setupLayout();
            setupEventListeners(client);
            setupFrame();
        }

        // 初始化登录界面的所有组件
        private void initComponents() {
            // 定义界面配色方案
            Color primaryColor = new Color(70, 130, 180);      // 钢蓝色
            Color secondaryColor = new Color(240, 248, 255);   // 爱丽丝蓝
            Color accentColor = new Color(255, 140, 0);        // 深橙色
            Color textColor = new Color(47, 79, 79);           // 深石板灰

            loginPanel = new JPanel();
            loginPanel.setBackground(secondaryColor);
            loginPanel.setBorder(new EmptyBorder(20, 20, 20, 20));

            // 创建字体
            Font labelFont = new Font("微软雅黑", Font.BOLD, 14);
            Font fieldFont = new Font("微软雅黑", Font.PLAIN, 13);
            Font buttonFont = new Font("微软雅黑", Font.BOLD, 13);

            // 输入框样式
            usernameField = new JTextField();
            usernameField.setFont(fieldFont);
            usernameField.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(primaryColor, 1),
                BorderFactory.createEmptyBorder(5, 8, 5, 8)));
            usernameField.setPreferredSize(new Dimension(200, 35));

            passwordField = new JPasswordField();
            passwordField.setFont(fieldFont);
            passwordField.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(primaryColor, 1),
                BorderFactory.createEmptyBorder(5, 8, 5, 8)));
            passwordField.setPreferredSize(new Dimension(200, 35));

            serverAddressField = new JTextField("localhost");
            serverAddressField.setFont(fieldFont);
            serverAddressField.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(primaryColor, 1),
                BorderFactory.createEmptyBorder(5, 8, 5, 8)));
            serverAddressField.setPreferredSize(new Dimension(150, 35));

            portField = new JTextField("8888");
            portField.setFont(fieldFont);
            portField.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(primaryColor, 1),
                BorderFactory.createEmptyBorder(5, 8, 5, 8)));
            portField.setPreferredSize(new Dimension(80, 35));

            // 按钮样式
            LoginButton = createStyledButton("登录", primaryColor, Color.WHITE, buttonFont);
            ExitButton = createStyledButton("退出", Color.GRAY, Color.WHITE, buttonFont);
            testButton = createStyledButton("测试连接", accentColor, Color.WHITE, buttonFont);

            // 标签样式
            usernameLabel = createStyledLabel("用户名:", labelFont, textColor);
            passwordLabel = createStyledLabel("密码:", labelFont, textColor);
            serverAddressLabel = createStyledLabel("服务器地址:", labelFont, textColor);
            portLabel = createStyledLabel("端口:", labelFont, textColor);

            picLabel = new JLabel();
        }

        // 创建样式化按钮的辅助方法
        private JButton createStyledButton(String text, Color bgColor, Color fgColor, Font font) {
            JButton button = new JButton(text);
            button.setFont(font);
            button.setBackground(bgColor);
            button.setForeground(fgColor);
            button.setBorder(BorderFactory.createEmptyBorder(10, 20, 10, 20));
            button.setFocusPainted(false);
            button.setCursor(new Cursor(Cursor.HAND_CURSOR));
            button.setPreferredSize(new Dimension(100, 40));

            // 添加鼠标悬停效果
            button.addMouseListener(new java.awt.event.MouseAdapter() {
                public void mouseEntered(java.awt.event.MouseEvent evt) {
                    button.setBackground(bgColor.brighter());
                }
                public void mouseExited(java.awt.event.MouseEvent evt) {
                    button.setBackground(bgColor);
                }
            });

            return button;
        }

        // 创建样式化标签的辅助方法
        private JLabel createStyledLabel(String text, Font font, Color color) {
            JLabel label = new JLabel(text);
            label.setFont(font);
            label.setForeground(color);
            return label;
        }

        private void setupLayout() {
            // 设置布局
            GroupLayout layout = new GroupLayout(loginPanel);
            loginPanel.setLayout(layout);
            layout.setHorizontalGroup(
                    layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                            .addGroup(layout.createSequentialGroup()
                                    .addGap(50, 50, 50)
                                    .addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                                            .addGroup(layout.createSequentialGroup()
                                                    .addComponent(serverAddressLabel)
                                                    .addComponent(serverAddressField, 150, 150, 150))
                                            .addGroup(layout.createSequentialGroup()
                                                    .addComponent(portLabel)
                                                    .addComponent(portField, 150, 150, 150)
                                                    .addComponent(testButton))
                                            .addGroup(layout.createSequentialGroup()
                                                    .addComponent(usernameLabel)
                                                    .addComponent(usernameField, 150, 150, 150))
                                            .addGroup(layout.createSequentialGroup()
                                                    .addComponent(passwordLabel)
                                                    .addComponent(passwordField, 150, 150, 150))
                                            .addGroup(layout.createSequentialGroup()
                                                    .addComponent(LoginButton)
                                                    .addComponent(ExitButton)))
                                    .addGap(50, 50, 50))
            );
            layout.setVerticalGroup(
                    layout.createSequentialGroup()
                            .addGap(30, 30, 30)
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(serverAddressLabel)
                                    .addComponent(serverAddressField))
                            .addGap(10, 10, 10)
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(portLabel)
                                    .addComponent(portField)
                                    .addComponent(testButton))
                            .addGap(20, 20, 20)
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(usernameLabel)
                                    .addComponent(usernameField))
                            .addGap(10, 10, 10)
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(passwordLabel)
                                    .addComponent(passwordField))
                            .addGap(20, 20, 20)
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(LoginButton)
                                    .addComponent(ExitButton))
                            .addGap(30, 30, 30)
            );
        }

        private void setupEventListeners(final Client client) {
            LoginButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    if (client.connect(serverAddressField.getText(), portField.getText(), false)) {
                        String username = usernameField.getText();
                        String password = new String(passwordField.getPassword());
                        String result = client.authenticate(username, password);
                        if (result == null) {
                            dispose();
                            client.loop();
                        } else
                            JOptionPane.showMessageDialog(LoginFrame.this, result, "登录失败", JOptionPane.ERROR_MESSAGE);
                    } else
                        JOptionPane.showMessageDialog(LoginFrame.this, "服务器连接错误", "连接错误", JOptionPane.ERROR_MESSAGE);
                }
            });

            ExitButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    System.exit(0);
                }
            });

            testButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    if (client.connect(serverAddressField.getText(), portField.getText(), true))
                        JOptionPane.showMessageDialog(LoginFrame.this, "服务器连接成功，请输入账号密码", "测试连接成功", JOptionPane.INFORMATION_MESSAGE);
                    else
                        JOptionPane.showMessageDialog(LoginFrame.this, "服务器连接错误", "连接错误", JOptionPane.ERROR_MESSAGE);
                }
            });
        }

        private void setupFrame() {
            try {
                setIconImage(new ImageIcon(ChatRoomShared.Constants.ICON_FILE).getImage());
            } catch (Exception e) {
                // 图标加载失败时忽略
            }
            setContentPane(loginPanel);
            setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            setSize(500, 400);  // 设置登录窗口尺寸
            setLocationRelativeTo(null);
            setVisible(true);
        }
    }

    // 聊天室主界面窗口
    public static class ClientView extends JFrame {
        private final Client client;  // 客户端实例引用
        private final DefaultListModel<String> onlineUserListModel = new DefaultListModel<String>();

        // 主要界面组件
        private JPanel chatRoom;
        private JTextField inputField;  // 消息输入框
        private JButton sendButton;
        private JLabel currentUsername;  // 显示当前登录用户
        private JComboBox<String> anonymousSelect;  // 匿名模式选择
        private JList<String> onlineUserList;  // 在线用户列表
        private JButton logoutButton;
        private JTextPane chatContent;  // 聊天内容显示区
        private JScrollPane onlineUserPanel;
        private JScrollPane messagePanel;
        private JPanel userInfoPanel;
        private JLabel onlineUserLabel;
        private JPanel chatPanel;
        private JLabel chatLabel;
        private JLabel inputLabel;
        private JLabel authorLabel;

        public ClientView(Client client) {
            super("简易聊天室-主界面");
            this.client = client;
            initComponents();
            setupEventListeners();
            setupFrame();
        }

        private void initComponents() {
            // 设置现代化的颜色主题
            Color primaryColor = new Color(70, 130, 180);      // 钢蓝色
            Color secondaryColor = new Color(240, 248, 255);   // 爱丽丝蓝
            Color accentColor = new Color(255, 140, 0);        // 深橙色
            Color textColor = new Color(47, 79, 79);           // 深石板灰
            Color chatBgColor = new Color(250, 250, 250);      // 聊天背景色

            // 创建字体
            Font labelFont = new Font("微软雅黑", Font.BOLD, 14);
            Font fieldFont = new Font("微软雅黑", Font.PLAIN, 13);
            Font buttonFont = new Font("微软雅黑", Font.BOLD, 13);
            Font chatFont = new Font("微软雅黑", Font.PLAIN, 14);

            chatRoom = new JPanel();
            chatRoom.setBackground(secondaryColor);
            chatRoom.setBorder(new EmptyBorder(15, 15, 15, 15));

            // 输入框样式
            inputField = new JTextField();
            inputField.setFont(fieldFont);
            inputField.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(primaryColor, 1),
                BorderFactory.createEmptyBorder(8, 12, 8, 12)));
            inputField.setPreferredSize(new Dimension(400, 40));

            // 按钮样式
            sendButton = createStyledButton("发送", primaryColor, Color.WHITE, buttonFont);
            logoutButton = createStyledButton("退出", Color.GRAY, Color.WHITE, buttonFont);

            // 用户信息标签
            currentUsername = new JLabel("当前用户: " + client.getUsername());
            currentUsername.setFont(labelFont);
            currentUsername.setForeground(textColor);
            currentUsername.setBorder(new EmptyBorder(5, 0, 5, 0));

            // 匿名选择下拉框
            anonymousSelect = new JComboBox<String>(new String[]{"实名", "匿名"});
            anonymousSelect.setFont(new Font("微软雅黑", Font.PLAIN, 12));  // 使用稍小的字体
            anonymousSelect.setBackground(Color.WHITE);
            anonymousSelect.setBorder(BorderFactory.createLineBorder(primaryColor, 1));
            anonymousSelect.setPreferredSize(new Dimension(150, 26));  // 与在线用户框保持一样宽度
            anonymousSelect.setMaximumSize(new Dimension(150, 26));    // 限制最大尺寸
            anonymousSelect.setMinimumSize(new Dimension(150, 26));    // 限制最小尺寸

            // 在线用户列表
            onlineUserList = new JList<String>(onlineUserListModel);
            onlineUserList.setFont(chatFont);
            onlineUserList.setBackground(Color.WHITE);
            onlineUserList.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
            onlineUserList.setSelectionBackground(primaryColor.brighter());
            onlineUserList.setCellRenderer(new DefaultListCellRenderer() {
                @Override
                public Component getListCellRendererComponent(JList<?> list, Object value, int index,
                        boolean isSelected, boolean cellHasFocus) {
                    super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
                    setBorder(new EmptyBorder(3, 8, 3, 8));
                    if (isSelected) {
                        setBackground(primaryColor);
                        setForeground(Color.WHITE);
                    }
                    return this;
                }
            });

            // 聊天内容区域
            chatContent = new JTextPane();
            chatContent.setEditable(false);
            chatContent.setBackground(chatBgColor);
            chatContent.setBorder(new EmptyBorder(10, 10, 10, 10));
            chatContent.setFont(chatFont);

            // 滚动面板
            onlineUserPanel = new JScrollPane(onlineUserList);
            onlineUserPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(primaryColor, 1), "在线用户",
                TitledBorder.LEFT, TitledBorder.TOP, labelFont, textColor));
            onlineUserPanel.setBackground(Color.WHITE);

            messagePanel = new JScrollPane(chatContent);
            messagePanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(primaryColor, 1), "聊天内容",
                TitledBorder.LEFT, TitledBorder.TOP, labelFont, textColor));
            messagePanel.setBackground(chatBgColor);

            // 标签
            onlineUserLabel = createStyledLabel("在线用户", labelFont, textColor);
            chatLabel = createStyledLabel("聊天内容", labelFont, textColor);
            inputLabel = createStyledLabel("输入消息:", labelFont, textColor);

            // 设置布局
            GroupLayout layout = new GroupLayout(chatRoom);
            chatRoom.setLayout(layout);
            layout.setHorizontalGroup(
                    layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                            .addGroup(layout.createSequentialGroup()
                                    .addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                                            .addComponent(chatLabel)
                                            .addComponent(messagePanel, 500, 500, 500)
                                            .addGroup(layout.createSequentialGroup()
                                                    .addComponent(inputLabel)
                                                    .addComponent(inputField, 400, 400, 400)
                                                    .addComponent(sendButton)))
                                    .addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                                            .addComponent(currentUsername)
                                            .addComponent(anonymousSelect, 150, 150, 150)
                                            .addComponent(onlineUserLabel)
                                            .addComponent(onlineUserPanel, 150, 150, 150)
                                            .addComponent(logoutButton)))
            );
            layout.setVerticalGroup(
                    layout.createSequentialGroup()
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(chatLabel)
                                    .addComponent(currentUsername))
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                                    .addGroup(layout.createSequentialGroup()
                                            .addComponent(messagePanel, 400, 400, 400)
                                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                                    .addComponent(inputLabel)
                                                    .addComponent(inputField)
                                                    .addComponent(sendButton)))
                                    .addGroup(layout.createSequentialGroup()
                                            .addComponent(anonymousSelect)
                                            .addComponent(onlineUserLabel)
                                            .addComponent(onlineUserPanel, 300, 300, 300)
                                            .addComponent(logoutButton)))
            );
        }

        private void setupEventListeners() {
            sendButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    send();
                }
            });
            inputField.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    send();
                }
            });

            anonymousSelect.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    client.setAnonymous(anonymousSelect.getSelectedIndex() == 1);
                }
            });

            addWindowListener(new WindowAdapter() {
                @Override
                public void windowClosing(WindowEvent e) {
                    client.handleCommand("quit");
                    client.stop();
                }
            });

            onlineUserList.addListSelectionListener(new ListSelectionListener() {
                @Override
                public void valueChanged(ListSelectionEvent e) {
                    if (!onlineUserList.isSelectionEmpty()) {
                        String selectedItem = onlineUserList.getSelectedValue();
                        if (inputField.getText().startsWith("@"))
                            JOptionPane.showMessageDialog(chatRoom, "输入框已经以 @ 开头", "操作错误", JOptionPane.ERROR_MESSAGE);
                        else
                            inputField.setText("@" + selectedItem + " " + inputField.getText());
                        onlineUserList.clearSelection();
                    }
                }
            });

            logoutButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    client.handleCommand("quit");
                    client.stop();
                }
            });
        }

        private void setupFrame() {
            try {
                setIconImage(new ImageIcon(ChatRoomShared.Constants.ICON_FILE).getImage());
            } catch (Exception e) {
                // 图标加载失败时忽略
            }
            setContentPane(chatRoom);
            setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            setSize(900, 700);  // 设置更大的窗口尺寸
            setLocationRelativeTo(null);
            setVisible(true);
        }

        private void send() {
            String content = inputField.getText().trim();
            client.handleUserInput(content);
            inputField.setText("");
        }

        public void setAnonymous(boolean isAnonymous) {
            anonymousSelect.setSelectedIndex(isAnonymous ? 1 : 0);
        }

        public void addUser(String username) {
            onlineUserListModel.addElement(username);
            onlineUserList.setModel(onlineUserListModel);
        }

        public void delUser(String username) {
            onlineUserListModel.removeElement(username);
            onlineUserList.setModel(onlineUserListModel);
        }

        public void setUserList(List<String> users) {
            onlineUserListModel.clear();
            for (String username : users)
                onlineUserListModel.addElement(username);
        }

        public void addTextMessage(ChatRoomShared.Message message, String username) {
            String prefix = ChatRoomShared.Messages.getMessagePrefix(message, username);
            String text = ChatRoomShared.Messages.getMessageContent(message);
            StyledDocument doc = chatContent.getStyledDocument();
            SimpleAttributeSet prefixStyle = new SimpleAttributeSet();
            StyleConstants.setBold(prefixStyle, true);
            StyleConstants.setFontFamily(prefixStyle, "微软雅黑");
            StyleConstants.setFontSize(prefixStyle, 14);
            if (message instanceof ChatRoomShared.SystemMessage)
                StyleConstants.setForeground(prefixStyle, new Color(70, 130, 180));  // 钢蓝色
            if (message instanceof ChatRoomShared.UserPrivateMessage)
                StyleConstants.setForeground(prefixStyle, new Color(255, 140, 0));   // 深橙色
            SimpleAttributeSet textStyle = new SimpleAttributeSet();
            StyleConstants.setFontFamily(textStyle, "微软雅黑");
            StyleConstants.setFontSize(textStyle, 14);
            if (message instanceof ChatRoomShared.UserMessage) {
                ChatRoomShared.UserMessage um = (ChatRoomShared.UserMessage) message;
                if (Objects.equals(um.getSender(), username)) {
                    StyleConstants.setAlignment(prefixStyle, StyleConstants.ALIGN_RIGHT);
                    StyleConstants.setAlignment(textStyle, StyleConstants.ALIGN_RIGHT);
                } else {
                    StyleConstants.setAlignment(prefixStyle, StyleConstants.ALIGN_LEFT);
                    StyleConstants.setAlignment(textStyle, StyleConstants.ALIGN_LEFT);
                }
            } else {
                StyleConstants.setAlignment(prefixStyle, StyleConstants.ALIGN_LEFT);
                StyleConstants.setAlignment(textStyle, StyleConstants.ALIGN_LEFT);
            }
            try {
                doc.insertString(doc.getLength(), prefix + "\n", prefixStyle);
                doc.setParagraphAttributes(doc.getLength() - prefix.length() - 1, prefix.length() + 1, prefixStyle, false);
                doc.insertString(doc.getLength(), "\t" + text + "\n" + "\n", textStyle);
                doc.setParagraphAttributes(doc.getLength() - text.length() - 3, text.length() + 3, textStyle, false);
            } catch (BadLocationException ex) {
                System.err.println("Error: " + ex.getMessage());
            }
            if (message instanceof ChatRoomShared.UserMessage) {
                ChatRoomShared.UserMessage um = (ChatRoomShared.UserMessage) message;
                if (Objects.equals(um.getSender(), username))
                    chatContent.setCaretPosition(chatContent.getDocument().getLength());
            }
        }

        public void displayMessage(String message) {
            StyledDocument doc = chatContent.getStyledDocument();
            SimpleAttributeSet textStyle = new SimpleAttributeSet();
            StyleConstants.setFontFamily(textStyle, "微软雅黑");
            StyleConstants.setFontSize(textStyle, 14);
            StyleConstants.setForeground(textStyle, new Color(255, 140, 0));  // 深橙色
            StyleConstants.setAlignment(textStyle, StyleConstants.ALIGN_CENTER);
            try {
                doc.insertString(doc.getLength(), message + "\n", textStyle);
                doc.setParagraphAttributes(doc.getLength() - message.length() - 1, message.length() + 1, textStyle, false);
            } catch (BadLocationException ex) {
                System.err.println("Error: " + ex.getMessage());
            }
            chatContent.setCaretPosition(chatContent.getDocument().getLength());
        }

        // 创建样式化按钮的辅助方法
        private JButton createStyledButton(String text, Color bgColor, Color fgColor, Font font) {
            JButton button = new JButton(text);
            button.setFont(font);
            button.setBackground(bgColor);
            button.setForeground(fgColor);
            button.setBorder(BorderFactory.createEmptyBorder(10, 20, 10, 20));
            button.setFocusPainted(false);
            button.setCursor(new Cursor(Cursor.HAND_CURSOR));
            button.setPreferredSize(new Dimension(100, 40));

            // 添加鼠标悬停效果
            button.addMouseListener(new java.awt.event.MouseAdapter() {
                public void mouseEntered(java.awt.event.MouseEvent evt) {
                    button.setBackground(bgColor.brighter());
                }
                public void mouseExited(java.awt.event.MouseEvent evt) {
                    button.setBackground(bgColor);
                }
            });

            return button;
        }

        // 创建样式化标签的辅助方法
        private JLabel createStyledLabel(String text, Font font, Color color) {
            JLabel label = new JLabel(text);
            label.setFont(font);
            label.setForeground(color);
            return label;
        }
    }

    // 客户端核心逻辑处理类
    public static class Client {
        // 网络连接相关
        private Socket socket;
        private ObjectInputStream in;
        private ObjectOutputStream out;

        // 用户状态
        private String username;
        private boolean isAnonymous = false;  // 匿名模式标志
        private ClientView clientView;

        public static void main(String[] args) {
            SwingUtilities.invokeLater(new Runnable() {
                @Override
                public void run() {
                    new LoginFrame(new Client());
                }
            });
        }

        public void sendSystemRequest(ChatRoomShared.MessageContent content) {
            try {
                out.writeObject(new ChatRoomShared.SystemRequest(username, content));
            } catch (IOException e) {
                System.err.println("Error sending system request: " + e.getMessage());
            }
        }

        public String authenticate(String username, String password) {
            this.username = username;
            sendSystemRequest(new ChatRoomShared.TextMessageContent(username));
            sendSystemRequest(new ChatRoomShared.TextMessageContent(password));
            try {
                ChatRoomShared.SystemReply serverMessage = (ChatRoomShared.SystemReply) in.readObject();
                String result = (String) serverMessage.getContent().getContent();
                if (Objects.equals(result, ChatRoomShared.SystemReply.LOGIN_SUCCESS)) {
                    return null;
                } else
                    return result;
            } catch (IOException e) {
                throw new RuntimeException(e);
            } catch (ClassNotFoundException e) {
                throw new RuntimeException(e);
            }
        }

        public void handleCommand(String command) {
            switch (command.toLowerCase()) {
                case "list":
                    sendSystemRequest(new ChatRoomShared.TextMessageContent("list"));
                    break;
                case "quit":
                    sendSystemRequest(new ChatRoomShared.TextMessageContent("quit"));
                    stop();
                    System.exit(0);
                    break;
                case "showanonymous":
                    if (clientView != null) {
                        clientView.displayMessage("Current chat mode: " + (isAnonymous ? "Anonymous" : "Named"));
                    }
                    break;
                case "anonymous":
                    setAnonymous(!isAnonymous);
                    if (clientView != null) {
                        clientView.displayMessage("Chat mode changed to: " + (isAnonymous ? "Anonymous" : "Named"));
                    }
                    break;
                default:
                    if (clientView != null) {
                        clientView.displayMessage("Unknown command. Available commands: list, quit, showanonymous, anonymous");
                    }
            }
        }

        public void setAnonymous(boolean anonymous) {
            isAnonymous = anonymous;
            if (clientView != null) {
                clientView.setAnonymous(anonymous);
            }
        }

        public boolean connect(String address, String port, boolean isTest) {
            try {
                socket = new Socket(address, Integer.parseInt(port));
                out = new ObjectOutputStream(socket.getOutputStream());
                in = new ObjectInputStream(socket.getInputStream());
                if (isTest) {
                    in.close();
                    in = null;
                    out.close();
                    out = null;
                    socket.close();
                    socket = null;
                }
            } catch (IOException e) {
                System.err.println("Error connecting to " + address + ":" + port);
                return false;
            }
            return true;
        }

        public void loop() {
            SwingWorker<Void, ChatRoomShared.Message> worker = new SwingWorker<Void, ChatRoomShared.Message>() {
                @Override
                protected Void doInBackground() {
                    try {
                        ChatRoomShared.Message serverMessage;
                        while ((serverMessage = (ChatRoomShared.Message) in.readObject()) != null)
                            publish(serverMessage);
                    } catch (Exception ignored) {
                        if (clientView != null) {
                            JOptionPane.showMessageDialog(clientView, "服务器连接错误", "服务器连接错误", JOptionPane.ERROR_MESSAGE);
                        }
                        stop();
                    }
                    return null;
                }

                @Override
                protected void process(List<ChatRoomShared.Message> chunks) {
                    for (ChatRoomShared.Message message : chunks)
                        handleServerMessage(message);
                }
            };
            worker.execute();
            clientView = new ClientView(this);
            clientView.setAnonymous(isAnonymous);
            sendSystemRequest(new ChatRoomShared.TextMessageContent("list"));
        }

        public void stop() {
            try {
                if (in != null) in.close();
                if (out != null) out.close();
                if (socket != null) socket.close();
            } catch (IOException e) {
                System.err.println("Error closing client connection: " + e.getMessage());
            }
            System.exit(0);
        }

        // 处理用户在输入框中的输入
        public void handleUserInput(String content) {
            if (content.isEmpty()) {
                if (clientView != null) {
                    JOptionPane.showMessageDialog(clientView, "输入不能为空", "发送错误", JOptionPane.ERROR_MESSAGE);
                }
                return;
            }
            // 以@@开头的是客户端命令
            if (content.startsWith("@@"))
                handleCommand(content.substring(2));
            else
                handleUserMessage(content);
        }

        // 处理用户发送的聊天消息
        public void handleUserMessage(String content) {
            ChatRoomShared.Message message;
            // 以@开头的是私聊消息
            if (content.startsWith("@")) {
                int spaceIndex = content.indexOf(' ');
                if (spaceIndex != -1) {
                    String recipient = content.substring(1, spaceIndex);
                    if (recipient.equals(username)) {
                        if (clientView != null) {
                            JOptionPane.showMessageDialog(clientView, "不能给自己发私信", "发送错误", JOptionPane.ERROR_MESSAGE);
                        }
                        return;
                    }
                    String privateMessage = content.substring(spaceIndex + 1);
                    message = new ChatRoomShared.UserPrivateMessage(username, isAnonymous, recipient, new ChatRoomShared.TextMessageContent(privateMessage));
                } else {
                    if (clientView != null) {
                        JOptionPane.showMessageDialog(clientView, "消息不能为空", "发送错误", JOptionPane.ERROR_MESSAGE);
                    }
                    return;
                }
            } else
                // 普通群聊消息
                message = new ChatRoomShared.UserBroadcastMessage(username, isAnonymous, new ChatRoomShared.TextMessageContent(content));
            try {
                out.writeObject(message);
            } catch (IOException e) {
                System.err.println("Error sending message: " + e.getMessage());
            }
        }

        public void handleServerMessage(ChatRoomShared.Message message) {
            if (message instanceof ChatRoomShared.SystemBroadcast) {
                ChatRoomShared.SystemBroadcast sb = (ChatRoomShared.SystemBroadcast) message;
                if (Objects.equals(sb.getBroadcastType(), "join"))
                    clientView.addUser(sb.getUsername());
                if (Objects.equals(sb.getBroadcastType(), "left"))
                    clientView.delUser(sb.getUsername());
            }
            if (message instanceof ChatRoomShared.SystemUserList) {
                ChatRoomShared.SystemUserList sul = (ChatRoomShared.SystemUserList) message;
                clientView.setUserList(sul.getUsers());
                return;
            }
            clientView.addTextMessage(message, username);
        }

        public String getUsername() {
            return username;
        }
    }
}
