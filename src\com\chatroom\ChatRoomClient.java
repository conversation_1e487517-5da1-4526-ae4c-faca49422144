package com.chatroom;

import javax.swing.*;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import javax.swing.text.BadLocationException;
import javax.swing.text.SimpleAttributeSet;
import javax.swing.text.StyleConstants;
import javax.swing.text.StyledDocument;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.net.Socket;
import java.util.List;
import java.util.Objects;

/**
 * 聊天室客户端 - 包含所有客户端相关功能
 * Java 8 兼容版本
 */
public class ChatRoomClient {

    // ==================== 登录界面 ====================
    public static class LoginFrame extends J<PERSON>rame {
        private JTextField usernameField;
        private JPasswordField passwordField;
        private JButton LoginButton;
        private JButton ExitButton;
        private JPanel loginPanel;
        private JTextField serverAddressField;
        private JTextField portField;
        private JLabel usernameLabel;
        private JLabel passwordLabel;
        private JLabel serverAddressLabel;
        private JLabel portLabel;
        private JButton testButton;
        private JLabel picLabel;

        public LoginFrame(Client client) {
            super("简易聊天室");
            initComponents();
            setupEventListeners(client);
            setupFrame();
        }

        private void initComponents() {
            loginPanel = new JPanel();
            usernameField = new JTextField();
            passwordField = new JPasswordField();
            LoginButton = new JButton("登录");
            ExitButton = new JButton("退出");
            serverAddressField = new JTextField("localhost");
            portField = new JTextField("8888");
            usernameLabel = new JLabel("用户名:");
            passwordLabel = new JLabel("密码:");
            serverAddressLabel = new JLabel("服务器地址:");
            portLabel = new JLabel("端口:");
            testButton = new JButton("测试连接");
            picLabel = new JLabel();

            // 设置布局
            GroupLayout layout = new GroupLayout(loginPanel);
            loginPanel.setLayout(layout);
            layout.setHorizontalGroup(
                    layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                            .addGroup(layout.createSequentialGroup()
                                    .addGap(50, 50, 50)
                                    .addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                                            .addGroup(layout.createSequentialGroup()
                                                    .addComponent(serverAddressLabel)
                                                    .addComponent(serverAddressField, 150, 150, 150))
                                            .addGroup(layout.createSequentialGroup()
                                                    .addComponent(portLabel)
                                                    .addComponent(portField, 150, 150, 150)
                                                    .addComponent(testButton))
                                            .addGroup(layout.createSequentialGroup()
                                                    .addComponent(usernameLabel)
                                                    .addComponent(usernameField, 150, 150, 150))
                                            .addGroup(layout.createSequentialGroup()
                                                    .addComponent(passwordLabel)
                                                    .addComponent(passwordField, 150, 150, 150))
                                            .addGroup(layout.createSequentialGroup()
                                                    .addComponent(LoginButton)
                                                    .addComponent(ExitButton)))
                                    .addGap(50, 50, 50))
            );
            layout.setVerticalGroup(
                    layout.createSequentialGroup()
                            .addGap(30, 30, 30)
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(serverAddressLabel)
                                    .addComponent(serverAddressField))
                            .addGap(10, 10, 10)
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(portLabel)
                                    .addComponent(portField)
                                    .addComponent(testButton))
                            .addGap(20, 20, 20)
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(usernameLabel)
                                    .addComponent(usernameField))
                            .addGap(10, 10, 10)
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(passwordLabel)
                                    .addComponent(passwordField))
                            .addGap(20, 20, 20)
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(LoginButton)
                                    .addComponent(ExitButton))
                            .addGap(30, 30, 30)
            );
        }

        private void setupEventListeners(final Client client) {
            LoginButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    if (client.connect(serverAddressField.getText(), portField.getText(), false)) {
                        String username = usernameField.getText();
                        String password = new String(passwordField.getPassword());
                        String result = client.authenticate(username, password);
                        if (result == null) {
                            dispose();
                            client.loop();
                        } else
                            JOptionPane.showMessageDialog(LoginFrame.this, result, "登录失败", JOptionPane.ERROR_MESSAGE);
                    } else
                        JOptionPane.showMessageDialog(LoginFrame.this, "服务器连接错误", "连接错误", JOptionPane.ERROR_MESSAGE);
                }
            });

            ExitButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    System.exit(0);
                }
            });

            testButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    if (client.connect(serverAddressField.getText(), portField.getText(), true))
                        JOptionPane.showMessageDialog(LoginFrame.this, "服务器连接成功，请输入账号密码", "测试连接成功", JOptionPane.INFORMATION_MESSAGE);
                    else
                        JOptionPane.showMessageDialog(LoginFrame.this, "服务器连接错误", "连接错误", JOptionPane.ERROR_MESSAGE);
                }
            });
        }

        private void setupFrame() {
            try {
                // 直接使用GIF文件作为图标，Swing会自动处理
                ImageIcon icon = new ImageIcon(ChatRoomShared.Constants.ICON_FILE);
                if (icon.getIconWidth() > 0 && icon.getIconHeight() > 0) {
                    setIconImage(icon.getImage());
                }
            } catch (Exception e) {
                // 图标加载失败时忽略，使用默认图标
                System.err.println("Failed to load icon: " + e.getMessage());
            }
            setContentPane(loginPanel);
            setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            pack();
            setLocationRelativeTo(null);
            setVisible(true);
        }
    }

    // ==================== 客户端主界面 ====================
    public static class ClientView extends JFrame {
        private final Client client;
        private final DefaultListModel<String> onlineUserListModel = new DefaultListModel<String>();
        private JPanel chatRoom;
        private JTextField inputField;
        private JButton sendButton;
        private JLabel currentUsername;
        private JComboBox<String> anonymousSelect;
        private JList<String> onlineUserList;
        private JButton logoutButton;
        private JTextPane chatContent;
        private JScrollPane onlineUserPanel;
        private JScrollPane messagePanel;
        private JPanel userInfoPanel;
        private JLabel onlineUserLabel;
        private JPanel chatPanel;
        private JLabel chatLabel;
        private JLabel inputLabel;
        private JLabel authorLabel;

        public ClientView(Client client) {
            super("简易聊天室-主界面");
            this.client = client;
            initComponents();
            setupEventListeners();
            setupFrame();
        }

        private void initComponents() {
            chatRoom = new JPanel();
            inputField = new JTextField();
            sendButton = new JButton("发送");
            currentUsername = new JLabel("当前用户: " + client.getUsername());
            anonymousSelect = new JComboBox<String>(new String[]{"实名", "匿名"});
            onlineUserList = new JList<String>(onlineUserListModel);
            logoutButton = new JButton("退出");
            chatContent = new JTextPane();
            chatContent.setEditable(false);

            onlineUserPanel = new JScrollPane(onlineUserList);
            messagePanel = new JScrollPane(chatContent);

            userInfoPanel = new JPanel();
            onlineUserLabel = new JLabel("在线用户");
            chatPanel = new JPanel();
            chatLabel = new JLabel("聊天内容");
            inputLabel = new JLabel("输入:");
            authorLabel = new JLabel("作者信息");

            // 设置布局
            GroupLayout layout = new GroupLayout(chatRoom);
            chatRoom.setLayout(layout);
            layout.setHorizontalGroup(
                    layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                            .addGroup(layout.createSequentialGroup()
                                    .addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                                            .addComponent(chatLabel)
                                            .addComponent(messagePanel, 500, 500, 500)
                                            .addGroup(layout.createSequentialGroup()
                                                    .addComponent(inputLabel)
                                                    .addComponent(inputField, 400, 400, 400)
                                                    .addComponent(sendButton)))
                                    .addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                                            .addComponent(currentUsername)
                                            .addComponent(anonymousSelect)
                                            .addComponent(onlineUserLabel)
                                            .addComponent(onlineUserPanel, 150, 150, 150)
                                            .addComponent(logoutButton)))
            );
            layout.setVerticalGroup(
                    layout.createSequentialGroup()
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(chatLabel)
                                    .addComponent(currentUsername))
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                                    .addGroup(layout.createSequentialGroup()
                                            .addComponent(messagePanel, 400, 400, 400)
                                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                                    .addComponent(inputLabel)
                                                    .addComponent(inputField)
                                                    .addComponent(sendButton)))
                                    .addGroup(layout.createSequentialGroup()
                                            .addComponent(anonymousSelect)
                                            .addComponent(onlineUserLabel)
                                            .addComponent(onlineUserPanel, 300, 300, 300)
                                            .addComponent(logoutButton)))
            );
        }

        private void setupEventListeners() {
            sendButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    send();
                }
            });
            inputField.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    send();
                }
            });

            anonymousSelect.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    client.setAnonymous(anonymousSelect.getSelectedIndex() == 1);
                }
            });

            addWindowListener(new WindowAdapter() {
                @Override
                public void windowClosing(WindowEvent e) {
                    client.handleCommand("quit");
                    client.stop();
                }
            });

            onlineUserList.addListSelectionListener(new ListSelectionListener() {
                @Override
                public void valueChanged(ListSelectionEvent e) {
                    if (!onlineUserList.isSelectionEmpty()) {
                        String selectedItem = onlineUserList.getSelectedValue();
                        if (inputField.getText().startsWith("@"))
                            JOptionPane.showMessageDialog(chatRoom, "输入框已经以 @ 开头", "操作错误", JOptionPane.ERROR_MESSAGE);
                        else
                            inputField.setText("@" + selectedItem + " " + inputField.getText());
                        onlineUserList.clearSelection();
                    }
                }
            });

            logoutButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    client.handleCommand("quit");
                    client.stop();
                }
            });
        }

        private void setupFrame() {
            try {
                // 直接使用GIF文件作为图标，Swing会自动处理
                ImageIcon icon = new ImageIcon(ChatRoomShared.Constants.ICON_FILE);
                if (icon.getIconWidth() > 0 && icon.getIconHeight() > 0) {
                    setIconImage(icon.getImage());
                }
            } catch (Exception e) {
                // 图标加载失败时忽略，使用默认图标
                System.err.println("Failed to load icon: " + e.getMessage());
            }
            setContentPane(chatRoom);
            setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            pack();
            setLocationRelativeTo(null);
            setVisible(true);
        }

        private void send() {
            String content = inputField.getText().trim();
            client.handleUserInput(content);
            inputField.setText("");
        }

        public void setAnonymous(boolean isAnonymous) {
            anonymousSelect.setSelectedIndex(isAnonymous ? 1 : 0);
        }

        public void addUser(String username) {
            onlineUserListModel.addElement(username);
            onlineUserList.setModel(onlineUserListModel);
        }

        public void delUser(String username) {
            onlineUserListModel.removeElement(username);
            onlineUserList.setModel(onlineUserListModel);
        }

        public void setUserList(List<String> users) {
            onlineUserListModel.clear();
            for (String username : users)
                onlineUserListModel.addElement(username);
        }

        public void addTextMessage(ChatRoomShared.Message message, String username) {
            String prefix = ChatRoomShared.Messages.getMessagePrefix(message, username);
            String text = ChatRoomShared.Messages.getMessageContent(message);
            StyledDocument doc = chatContent.getStyledDocument();
            SimpleAttributeSet prefixStyle = new SimpleAttributeSet();
            StyleConstants.setBold(prefixStyle, true);
            StyleConstants.setFontFamily(prefixStyle, "Consolas");
            StyleConstants.setFontSize(prefixStyle, 20);
            if (message instanceof ChatRoomShared.SystemMessage)
                StyleConstants.setForeground(prefixStyle, Color.BLUE);
            if (message instanceof ChatRoomShared.UserPrivateMessage)
                StyleConstants.setForeground(prefixStyle, new Color(118, 3, 137));
            SimpleAttributeSet textStyle = new SimpleAttributeSet();
            StyleConstants.setFontFamily(textStyle, "SansSerif");
            StyleConstants.setFontSize(textStyle, 20);
            if (message instanceof ChatRoomShared.UserMessage) {
                ChatRoomShared.UserMessage um = (ChatRoomShared.UserMessage) message;
                if (Objects.equals(um.getSender(), username)) {
                    StyleConstants.setAlignment(prefixStyle, StyleConstants.ALIGN_RIGHT);
                    StyleConstants.setAlignment(textStyle, StyleConstants.ALIGN_RIGHT);
                } else {
                    StyleConstants.setAlignment(prefixStyle, StyleConstants.ALIGN_LEFT);
                    StyleConstants.setAlignment(textStyle, StyleConstants.ALIGN_LEFT);
                }
            } else {
                StyleConstants.setAlignment(prefixStyle, StyleConstants.ALIGN_LEFT);
                StyleConstants.setAlignment(textStyle, StyleConstants.ALIGN_LEFT);
            }
            try {
                doc.insertString(doc.getLength(), prefix + "\n", prefixStyle);
                doc.setParagraphAttributes(doc.getLength() - prefix.length() - 1, prefix.length() + 1, prefixStyle, false);
                doc.insertString(doc.getLength(), "\t" + text + "\n" + "\n", textStyle);
                doc.setParagraphAttributes(doc.getLength() - text.length() - 3, text.length() + 3, textStyle, false);
            } catch (BadLocationException ex) {
                System.err.println("Error: " + ex.getMessage());
            }
            if (message instanceof ChatRoomShared.UserMessage) {
                ChatRoomShared.UserMessage um = (ChatRoomShared.UserMessage) message;
                if (Objects.equals(um.getSender(), username))
                    chatContent.setCaretPosition(chatContent.getDocument().getLength());
            }
        }

        public void displayMessage(String message) {
            StyledDocument doc = chatContent.getStyledDocument();
            SimpleAttributeSet textStyle = new SimpleAttributeSet();
            StyleConstants.setFontFamily(textStyle, "Consolas");
            StyleConstants.setFontSize(textStyle, 20);
            StyleConstants.setForeground(textStyle, Color.MAGENTA);
            StyleConstants.setAlignment(textStyle, StyleConstants.ALIGN_CENTER);
            try {
                doc.insertString(doc.getLength(), message + "\n", textStyle);
                doc.setParagraphAttributes(doc.getLength() - message.length() - 1, message.length() + 1, textStyle, false);
            } catch (BadLocationException ex) {
                System.err.println("Error: " + ex.getMessage());
            }
            chatContent.setCaretPosition(chatContent.getDocument().getLength());
        }
    }

    // ==================== 客户端主类 ====================
    public static class Client {
        private Socket socket;
        private ObjectInputStream in;
        private ObjectOutputStream out;
        private String username;
        private boolean isAnonymous = false;
        private ClientView clientView;

        public static void main(String[] args) {
            SwingUtilities.invokeLater(new Runnable() {
                @Override
                public void run() {
                    new LoginFrame(new Client());
                }
            });
        }

        public void sendSystemRequest(ChatRoomShared.MessageContent content) {
            try {
                out.writeObject(new ChatRoomShared.SystemRequest(username, content));
            } catch (IOException e) {
                System.err.println("Error sending system request: " + e.getMessage());
            }
        }

        public String authenticate(String username, String password) {
            this.username = username;
            sendSystemRequest(new ChatRoomShared.TextMessageContent(username));
            sendSystemRequest(new ChatRoomShared.TextMessageContent(password));
            try {
                ChatRoomShared.SystemReply serverMessage = (ChatRoomShared.SystemReply) in.readObject();
                String result = (String) serverMessage.getContent().getContent();
                if (Objects.equals(result, ChatRoomShared.SystemReply.LOGIN_SUCCESS)) {
                    return null;
                } else
                    return result;
            } catch (IOException e) {
                throw new RuntimeException(e);
            } catch (ClassNotFoundException e) {
                throw new RuntimeException(e);
            }
        }

        public void handleCommand(String command) {
            switch (command.toLowerCase()) {
                case "list":
                    sendSystemRequest(new ChatRoomShared.TextMessageContent("list"));
                    break;
                case "quit":
                    sendSystemRequest(new ChatRoomShared.TextMessageContent("quit"));
                    stop();
                    System.exit(0);
                    break;
                case "showanonymous":
                    if (clientView != null) {
                        clientView.displayMessage("Current chat mode: " + (isAnonymous ? "Anonymous" : "Named"));
                    }
                    break;
                case "anonymous":
                    setAnonymous(!isAnonymous);
                    if (clientView != null) {
                        clientView.displayMessage("Chat mode changed to: " + (isAnonymous ? "Anonymous" : "Named"));
                    }
                    break;
                default:
                    if (clientView != null) {
                        clientView.displayMessage("Unknown command. Available commands: list, quit, showanonymous, anonymous");
                    }
            }
        }

        public void setAnonymous(boolean anonymous) {
            isAnonymous = anonymous;
            if (clientView != null) {
                clientView.setAnonymous(anonymous);
            }
        }

        public boolean connect(String address, String port, boolean isTest) {
            try {
                socket = new Socket(address, Integer.parseInt(port));
                out = new ObjectOutputStream(socket.getOutputStream());
                in = new ObjectInputStream(socket.getInputStream());
                if (isTest) {
                    in.close();
                    in = null;
                    out.close();
                    out = null;
                    socket.close();
                    socket = null;
                }
            } catch (IOException e) {
                System.err.println("Error connecting to " + address + ":" + port);
                return false;
            }
            return true;
        }

        public void loop() {
            SwingWorker<Void, ChatRoomShared.Message> worker = new SwingWorker<Void, ChatRoomShared.Message>() {
                @Override
                protected Void doInBackground() {
                    try {
                        ChatRoomShared.Message serverMessage;
                        while ((serverMessage = (ChatRoomShared.Message) in.readObject()) != null)
                            publish(serverMessage);
                    } catch (Exception ignored) {
                        if (clientView != null) {
                            JOptionPane.showMessageDialog(clientView, "服务器连接错误", "服务器连接错误", JOptionPane.ERROR_MESSAGE);
                        }
                        stop();
                    }
                    return null;
                }

                @Override
                protected void process(List<ChatRoomShared.Message> chunks) {
                    for (ChatRoomShared.Message message : chunks)
                        handleServerMessage(message);
                }
            };
            worker.execute();
            clientView = new ClientView(this);
            clientView.setAnonymous(isAnonymous);
            sendSystemRequest(new ChatRoomShared.TextMessageContent("list"));
        }

        public void stop() {
            try {
                if (in != null) in.close();
                if (out != null) out.close();
                if (socket != null) socket.close();
            } catch (IOException e) {
                System.err.println("Error closing client connection: " + e.getMessage());
            }
            System.exit(0);
        }

        public void handleUserInput(String content) {
            if (content.isEmpty()) {
                if (clientView != null) {
                    JOptionPane.showMessageDialog(clientView, "输入不能为空", "发送错误", JOptionPane.ERROR_MESSAGE);
                }
                return;
            }
            if (content.startsWith("@@"))
                handleCommand(content.substring(2));
            else
                handleUserMessage(content);
        }

        public void handleUserMessage(String content) {
            ChatRoomShared.Message message;
            if (content.startsWith("@")) {
                int spaceIndex = content.indexOf(' ');
                if (spaceIndex != -1) {
                    String recipient = content.substring(1, spaceIndex);
                    if (recipient.equals(username)) {
                        if (clientView != null) {
                            JOptionPane.showMessageDialog(clientView, "不能给自己发私信", "发送错误", JOptionPane.ERROR_MESSAGE);
                        }
                        return;
                    }
                    String privateMessage = content.substring(spaceIndex + 1);
                    message = new ChatRoomShared.UserPrivateMessage(username, isAnonymous, recipient, new ChatRoomShared.TextMessageContent(privateMessage));
                } else {
                    if (clientView != null) {
                        JOptionPane.showMessageDialog(clientView, "消息不能为空", "发送错误", JOptionPane.ERROR_MESSAGE);
                    }
                    return;
                }
            } else
                message = new ChatRoomShared.UserBroadcastMessage(username, isAnonymous, new ChatRoomShared.TextMessageContent(content));
            try {
                out.writeObject(message);
            } catch (IOException e) {
                System.err.println("Error sending message: " + e.getMessage());
            }
        }

        public void handleServerMessage(ChatRoomShared.Message message) {
            if (message instanceof ChatRoomShared.SystemBroadcast) {
                ChatRoomShared.SystemBroadcast sb = (ChatRoomShared.SystemBroadcast) message;
                if (Objects.equals(sb.getBroadcastType(), "join"))
                    clientView.addUser(sb.getUsername());
                if (Objects.equals(sb.getBroadcastType(), "left"))
                    clientView.delUser(sb.getUsername());
            }
            if (message instanceof ChatRoomShared.SystemUserList) {
                ChatRoomShared.SystemUserList sul = (ChatRoomShared.SystemUserList) message;
                clientView.setUserList(sul.getUsers());
                return;
            }
            clientView.addTextMessage(message, username);
        }

        public String getUsername() {
            return username;
        }
    }
}
