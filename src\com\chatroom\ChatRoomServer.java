package com.chatroom;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;
import javax.swing.text.BadLocationException;
import javax.swing.text.StyledDocument;
import java.awt.Color;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 聊天室服务器端 - 包含所有服务器相关功能
 *
 */
public class ChatRoomServer {

    // 用户账户管理类
    public static class UserManager {
        private final Map<String, String> users;  // 用户名->密码映射表

        public UserManager(String userFilePath) {
            users = new HashMap<String, String>();
            loadUsers(userFilePath);
        }

        // 从文件中加载用户数据，格式为"用户名:密码"
        private void loadUsers(String userFilePath) {
            try (BufferedReader reader = new BufferedReader(new FileReader(userFilePath))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    String[] parts = line.split(":");
                    if (parts.length == 2) {
                        users.put(parts[0].trim(), parts[1].trim());
                    }
                }
            } catch (IOException e) {
                System.err.println("Error loading user file: " + e.getMessage());
            }
        }

        public boolean authenticate(String username, String password) {
            return users.containsKey(username) && users.get(username).equals(password);
        }

        public List<String> getAllUsers() {
            return new ArrayList<String>(users.keySet());
        }

        public boolean isUserExist(String username) {
            return users.containsKey(username);
        }
    }

    // 服务器日志记录工具
    public static class Logger {
        private final String logfile;  // 日志文件路径
        private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        public Logger(String logfile) {
            this.logfile = logfile;
        }

        public synchronized void log(String message) {
            String logMessage = String.format("[%s] %s", LocalDateTime.now().format(formatter), message);
            writeToFile(logMessage);
        }

        private void writeToFile(String message) {
            try (FileWriter fw = new FileWriter(logfile, true);
                 BufferedWriter bw = new BufferedWriter(fw);
                 PrintWriter out = new PrintWriter(bw)) {
                out.println(message);
            } catch (IOException e) {
                System.err.println("Error writing to log file: " + e.getMessage());
            }
        }
    }

    // 处理单个客户端连接的线程类
    public static class ClientHandler implements Runnable {
        private final Socket socket;  // 客户端连接
        private final Server server;  // 服务器实例引用
        private ObjectInputStream in;
        private ObjectOutputStream out;
        private String username;  // 客户端用户名
        private boolean authenticated = false;  // 是否已通过身份验证

        public ClientHandler(Socket socket, Server server) {
            this.socket = socket;
            this.server = server;
        }

        @Override
        public void run() {
            try {
                in = new ObjectInputStream(socket.getInputStream());
                out = new ObjectOutputStream(socket.getOutputStream());
                if (authenticate())
                    loop();
            } catch (IOException e) {
                server.output("Error handling client: " + e.getMessage());
            } catch (ClassNotFoundException e) {
                server.output("Error handling client: " + e.getMessage());
            } finally {
                close(false);
            }
        }

        private void loop() {
            try {
                ChatRoomShared.Message inputMessage;
                while ((inputMessage = (ChatRoomShared.Message) in.readObject()) != null) {
                    handleClientMessage(inputMessage);
                    if (socket.isClosed())
                        close(false);
                }
            } catch (SocketException ignored) {
            } catch (IOException e) {
                server.output("Error handling client: " + e.getMessage());
            } catch (ClassNotFoundException e) {
                server.output("Error handling client: " + e.getMessage());
            }
        }

        public void logLogin(String username, String ip, boolean success) {
            String status = success ? "successful" : "failed";
            server.output(String.format("Login %s for user %s from IP %s", status, username, ip));
        }

        public void logLogout(String username) {
            server.output(String.format("User %s logged out", username));
        }

        private boolean authenticate() throws IOException, ClassNotFoundException {
            while (!authenticated) {
                ChatRoomShared.SystemRequest usernameRequest = (ChatRoomShared.SystemRequest) in.readObject();
                String username = (String) usernameRequest.getContent().getContent();

                ChatRoomShared.SystemRequest passwordRequest = (ChatRoomShared.SystemRequest) in.readObject();
                String password = (String) passwordRequest.getContent().getContent();

                if (server.getUserManager().isUserExist(username)) {
                    if (server.getUserManager().authenticate(username, password)) {
                        if (!server.isUserAlreadyLogin(username)) {
                            authenticated = true;
                            this.username = username;
                            out.writeObject(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent(ChatRoomShared.SystemReply.LOGIN_SUCCESS)));
                            out.writeObject(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent("Authentication successful. Welcome to the chat room!")));
                            server.broadcastMessage(new ChatRoomShared.SystemBroadcast(new ChatRoomShared.TextMessageContent(username + " has joined the chat."), "join", username));
                            logLogin(username, ChatRoomShared.NetworkUtil.getIpAddress(socket), authenticated);
                            return true;
                        } else
                            out.writeObject(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent(ChatRoomShared.SystemReply.ALREADY_LOGIN)));
                    } else
                        out.writeObject(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent(ChatRoomShared.SystemReply.PASSWORD_INCORRECT)));
                } else
                    out.writeObject(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent(ChatRoomShared.SystemReply.USER_NOT_EXIST)));

                logLogin(username, ChatRoomShared.NetworkUtil.getIpAddress(socket), authenticated);
            }
            return false;
        }

        private void handleClientMessage(ChatRoomShared.Message message) {
            if (message instanceof ChatRoomShared.UserBroadcastMessage) {
                ChatRoomShared.UserBroadcastMessage ubm = (ChatRoomShared.UserBroadcastMessage) message;
                server.broadcastMessage(ubm);
            } else if (message instanceof ChatRoomShared.UserPrivateMessage) {
                ChatRoomShared.UserPrivateMessage upm = (ChatRoomShared.UserPrivateMessage) message;
                boolean userExist = server.isUserAlreadyLogin(upm.getReceiver());
                if (!userExist)
                    sendMessage(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent("User " + upm.getReceiver() + " is not online or existed. Please try again.")));
                else {
                    server.sendPrivateMessage(upm);
                    sendMessage(upm);
                }
            } else if (message instanceof ChatRoomShared.SystemRequest) {
                ChatRoomShared.SystemRequest sr = (ChatRoomShared.SystemRequest) message;
                if (!Objects.equals(username, sr.getUsername()))
                    throw new IllegalStateException("Unexpected value: " + sr);
                handleCommand((String) sr.getContent().getContent());
            } else {
                throw new IllegalStateException("Unexpected value: " + message);
            }
        }

        private void handleCommand(String command) {
            switch (command.toLowerCase()) {
                case "list":
                    sendMessage(new ChatRoomShared.SystemUserList(new ChatRoomShared.TextMessageContent(""), server.getOnlineUsers()));
                    sendMessage(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent("Online users: " + server.getOnlineUsers())));
                    break;
                case "quit":
                    close(false);
                    break;
                default:
                    sendMessage(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent("Unknown command. Available commands: list, quit")));
            }
        }

        public void sendMessage(ChatRoomShared.Message message) {
            try {
                out.writeObject(message);
            } catch (IOException e) {
                server.output("Error sending message: " + e.getMessage());
            }
        }

        public void close(boolean shutdown) {
            try {
                if (username != null) {
                    server.removeClient(this);
                    if (!shutdown)
                        server.broadcastMessage(new ChatRoomShared.SystemBroadcast(new ChatRoomShared.TextMessageContent(username + " has left the chat."), "left", username));
                    logLogout(username);
                    username = null;
                    authenticated = false;
                }
                socket.close();
            } catch (IOException e) {
                server.output("Error closing client connection: " + e.getMessage());
            }
        }

        public String getUsername() {
            return username;
        }

        public boolean isAuthenticated() {
            return authenticated;
        }
    }

    // ==================== 服务器视图 ====================
    public static class ServerView extends JFrame {
        private final Server server;
        private JTextPane messagePanel;
        private JPanel serverPanel;
        private JTextField inputField;
        private JButton sendButton;
        private JLabel inputLabel;
        private JScrollPane messageOuterPanel;

        public ServerView(Server server) {
            super("简易聊天室-服务器");
            this.server = server;
            initComponents();
            setupEventListeners();
            setupFrame();
        }

        private void initComponents() {
            // 设置现代化的颜色主题
            Color primaryColor = new Color(70, 130, 180);      // 钢蓝色
            Color secondaryColor = new Color(240, 248, 255);   // 爱丽丝蓝
            Color accentColor = new Color(255, 140, 0);        // 深橙色
            Color textColor = new Color(47, 79, 79);           // 深石板灰
            Color logBgColor = new Color(250, 250, 250);       // 日志背景色

            // 创建字体
            Font labelFont = new Font("微软雅黑", Font.BOLD, 14);
            Font fieldFont = new Font("微软雅黑", Font.PLAIN, 13);
            Font buttonFont = new Font("微软雅黑", Font.BOLD, 13);
            Font logFont = new Font("Consolas", Font.PLAIN, 14);

            serverPanel = new JPanel();
            serverPanel.setBackground(secondaryColor);
            serverPanel.setBorder(new EmptyBorder(15, 15, 15, 15));

            // 日志显示区域
            messagePanel = new JTextPane();
            messagePanel.setEditable(false);
            messagePanel.setBackground(logBgColor);
            messagePanel.setBorder(new EmptyBorder(10, 10, 10, 10));
            messagePanel.setFont(logFont);

            // 输入框样式
            inputField = new JTextField();
            inputField.setFont(fieldFont);
            inputField.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(primaryColor, 1),
                BorderFactory.createEmptyBorder(8, 12, 8, 12)));
            inputField.setPreferredSize(new Dimension(300, 35));

            // 按钮样式
            sendButton = createStyledButton("发送", primaryColor, Color.WHITE, buttonFont);

            // 标签样式
            inputLabel = createStyledLabel("服务器命令:", labelFont, textColor);

            // 滚动面板
            messageOuterPanel = new JScrollPane(messagePanel);
            messageOuterPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(primaryColor, 1), "服务器日志",
                TitledBorder.LEFT, TitledBorder.TOP, labelFont, textColor));
            messageOuterPanel.setBackground(logBgColor);

            // 设置布局
            GroupLayout layout = new GroupLayout(serverPanel);
            serverPanel.setLayout(layout);
            layout.setHorizontalGroup(
                    layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                            .addComponent(messageOuterPanel)
                            .addGroup(layout.createSequentialGroup()
                                    .addComponent(inputLabel)
                                    .addComponent(inputField)
                                    .addComponent(sendButton))
            );
            layout.setVerticalGroup(
                    layout.createSequentialGroup()
                            .addComponent(messageOuterPanel, 0, 400, Short.MAX_VALUE)
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(inputLabel)
                                    .addComponent(inputField, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)
                                    .addComponent(sendButton))
            );
        }

        private void setupEventListeners() {
            inputField.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    send();
                }
            });
            sendButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    send();
                }
            });
        }

        private void setupFrame() {
            try {
                setIconImage(new ImageIcon(ChatRoomShared.Constants.ICON_FILE).getImage());
            } catch (Exception e) {
                // 图标加载失败时忽略
            }
            setContentPane(serverPanel);
            setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            setSize(800, 600);  // 设置更大的窗口尺寸
            setLocationRelativeTo(null);
            setVisible(true);
        }

        private void send() {
            String content = inputField.getText().trim();
            server.handleServerCommands(content);
            inputField.setText("");
        }

        public void display(String message) {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            StyledDocument doc = messagePanel.getStyledDocument();

            // 创建样式
            javax.swing.text.SimpleAttributeSet timestampStyle = new javax.swing.text.SimpleAttributeSet();
            javax.swing.text.StyleConstants.setForeground(timestampStyle, new Color(128, 128, 128));  // 灰色时间戳
            javax.swing.text.StyleConstants.setFontFamily(timestampStyle, "Consolas");
            javax.swing.text.StyleConstants.setFontSize(timestampStyle, 14);

            javax.swing.text.SimpleAttributeSet messageStyle = new javax.swing.text.SimpleAttributeSet();
            javax.swing.text.StyleConstants.setForeground(messageStyle, new Color(47, 79, 79));  // 深石板灰
            javax.swing.text.StyleConstants.setFontFamily(messageStyle, "微软雅黑");
            javax.swing.text.StyleConstants.setFontSize(messageStyle, 14);

            try {
                // 插入时间戳
                doc.insertString(doc.getLength(), "[" + timestamp + "] ", timestampStyle);
                // 插入消息内容
                doc.insertString(doc.getLength(), message + "\n", messageStyle);
                messagePanel.setCaretPosition(doc.getLength());
            } catch (BadLocationException e) {
                throw new RuntimeException(e);
            }
        }

        // 创建样式化按钮的辅助方法
        private JButton createStyledButton(String text, Color bgColor, Color fgColor, Font font) {
            JButton button = new JButton(text);
            button.setFont(font);
            button.setBackground(bgColor);
            button.setForeground(fgColor);
            button.setBorder(BorderFactory.createEmptyBorder(10, 20, 10, 20));
            button.setFocusPainted(false);
            button.setCursor(new Cursor(Cursor.HAND_CURSOR));
            button.setPreferredSize(new Dimension(100, 40));

            // 添加鼠标悬停效果
            button.addMouseListener(new java.awt.event.MouseAdapter() {
                public void mouseEntered(java.awt.event.MouseEvent evt) {
                    button.setBackground(bgColor.brighter());
                }
                public void mouseExited(java.awt.event.MouseEvent evt) {
                    button.setBackground(bgColor);
                }
            });

            return button;
        }

        // 创建样式化标签的辅助方法
        private JLabel createStyledLabel(String text, Font font, Color color) {
            JLabel label = new JLabel(text);
            label.setFont(font);
            label.setForeground(color);
            return label;
        }
    }

    // 服务器核心管理类
    public static class Server {
        private final List<ClientHandler> clients;  // 已连接的客户端列表
        private final UserManager userManager;  // 用户管理器
        private final Logger logger;  // 日志记录器
        private final ExecutorService pool;  // 线程池
        private ServerSocket serverSocket;  // 服务器监听套接字
        private boolean running;  // 服务器运行状态
        private ServerView serverView;  // 服务器管理界面

        public Server() {
            this.userManager = new UserManager(ChatRoomShared.Constants.USER_FILE);
            this.logger = new Logger(ChatRoomShared.Constants.LOG_FILE);
            this.clients = new CopyOnWriteArrayList<ClientHandler>();
            this.pool = Executors.newCachedThreadPool();
            this.running = false;
        }

        public static void main(String[] args) {
            new Server().start();
        }

        public void output(String message) {
            if (serverView != null) {
                serverView.display(message);
            }
            logger.log(message);
        }

        // 等待并处理客户端连接的主循环
        private void waitForClient() {
            try {
                serverSocket = new ServerSocket(ChatRoomShared.Constants.PORT);
                running = true;
                output("Server started on port " + ChatRoomShared.Constants.PORT);

                while (running) {
                    Socket clientSocket = serverSocket.accept();  // 等待客户端连接
                    ClientHandler clientHandler = new ClientHandler(clientSocket, this);
                    clients.add(clientHandler);
                    pool.execute(clientHandler);  // 在线程池中处理客户端
                }
            } catch (SocketException ignored) {
                // 服务器正常关闭时会抛出此异常
            } catch (Exception e) {
                output("Error starting server: " + e.getMessage());
            } finally {
                stop();
            }
        }

        public void start() {
            serverView = new ServerView(this);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    waitForClient();
                }
            }).start();
        }

        public void stop() {
            running = false;
            try {
                if (serverSocket != null && !serverSocket.isClosed())
                    serverSocket.close();
                for (ClientHandler clientHandler : clients)
                    clientHandler.close(true);
                clients.clear();
                pool.shutdown();
            } catch (IOException e) {
                output("Error stopping server: " + e.getMessage());
            }
            System.exit(0);
        }

        public void broadcastMessage(ChatRoomShared.Message message) {
            for (ClientHandler client : clients) {
                if (client != null && client.isAuthenticated()) {
                    client.sendMessage(message);
                }
            }
        }

        public void sendPrivateMessage(ChatRoomShared.UserPrivateMessage message) {
            for (ClientHandler client : clients) {
                if (client != null && client.isAuthenticated() &&
                        Objects.equals(client.getUsername(), message.getReceiver())) {
                    client.sendMessage(message);
                }
            }
        }

        public void handleServerCommands(String command) {
            switch (command.toLowerCase()) {
                case "list":
                    output("Online users: " + getOnlineUsers());
                    break;
                case "listall":
                    output("All users: " + userManager.getAllUsers());
                    break;
                case "quit":
                    output("quit");
                    stop();
                    break;
                default:
                    output("Unknown command. Available commands: list, listall, quit");
            }
        }

        public List<String> getOnlineUsers() {
            List<String> onlineUsers = new ArrayList<String>();
            for (ClientHandler client : clients) {
                if (client != null && client.isAuthenticated()) {
                    onlineUsers.add(client.getUsername());
                }
            }
            return onlineUsers;
        }

        public boolean isUserAlreadyLogin(String username) {
            return getOnlineUsers().contains(username);
        }

        public UserManager getUserManager() {
            return userManager;
        }

        public void removeClient(ClientHandler clientHandler) {
            clients.remove(clientHandler);
        }
    }
}
