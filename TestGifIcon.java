import javax.swing.*;
import java.awt.*;

/**
 * 测试GIF图标是否能正常显示的简单程序
 */
public class TestGifIcon {
    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                createAndShowGUI();
            }
        });
    }
    
    private static void createAndShowGUI() {
        JFrame frame = new JFrame("GIF图标测试");
        
        try {
            // 测试加载GIF图标
            ImageIcon icon = new ImageIcon("resources/totoro.gif");
            System.out.println("GIF图标尺寸: " + icon.getIconWidth() + "x" + icon.getIconHeight());
            
            if (icon.getIconWidth() > 0 && icon.getIconHeight() > 0) {
                frame.setIconImage(icon.getImage());
                System.out.println("GIF图标加载成功！");
                
                // 在窗口中也显示这个GIF
                JLabel label = new JLabel(icon);
                frame.add(label);
            } else {
                System.out.println("GIF图标加载失败！");
                JLabel label = new JLabel("GIF图标加载失败");
                frame.add(label);
            }
        } catch (Exception e) {
            System.err.println("加载GIF图标时出错: " + e.getMessage());
            e.printStackTrace();
            JLabel label = new JLabel("错误: " + e.getMessage());
            frame.add(label);
        }
        
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(400, 300);
        frame.setLocationRelativeTo(null);
        frame.setVisible(true);
    }
}
