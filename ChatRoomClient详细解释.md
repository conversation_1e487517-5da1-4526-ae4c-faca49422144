# ChatRoomClient.java 详细解释

## 文件概述
`ChatRoomClient.java` 是聊天室项目的客户端主文件，包含了所有客户端相关的功能，包括登录界面、主聊天界面和客户端逻辑处理。

## 包声明和导入语句

```java
package com.chatroom;
```
- **第1行**: 声明包名为 `com.chatroom`，表示这个类属于chatroom包

```java
import javax.swing.*;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import javax.swing.text.BadLocationException;
import javax.swing.text.SimpleAttributeSet;
import javax.swing.text.StyleConstants;
import javax.swing.text.StyledDocument;
```
- **第3-9行**: 导入Swing相关的GUI组件和文本样式处理类
  - `JFrame`, `JButton`, `JTextField`等GUI组件
  - `ListSelectionEvent`, `ListSelectionListener`用于处理列表选择事件
  - `StyledDocument`, `SimpleAttributeSet`等用于富文本显示

```java
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
```
- **第10-14行**: 导入AWT相关的事件处理类
  - `ActionEvent`, `ActionListener`用于按钮点击事件
  - `WindowAdapter`, `WindowEvent`用于窗口关闭事件

```java
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.net.Socket;
import java.util.List;
import java.util.Objects;
```
- **第15-20行**: 导入网络通信和工具类
  - `Socket`用于网络连接
  - `ObjectInputStream`, `ObjectOutputStream`用于对象序列化传输
  - `List`, `Objects`用于数据处理

## 主类声明

```java
/**
 * 聊天室客户端 - 包含所有客户端相关功能
 * Java 8 兼容版本
 */
public class ChatRoomClient {
```
- **第22-26行**: 类的JavaDoc注释和类声明
  - 说明这是聊天室客户端的主类
  - 兼容Java 8版本

## LoginFrame 登录界面类

### 类声明和成员变量

```java
public static class LoginFrame extends JFrame {
    private JTextField usernameField;
    private JPasswordField passwordField;
    private JButton LoginButton;
    private JButton ExitButton;
    private JPanel loginPanel;
    private JTextField serverAddressField;
    private JTextField portField;
    private JLabel usernameLabel;
    private JLabel passwordLabel;
    private JLabel serverAddressLabel;
    private JLabel portLabel;
    private JButton testButton;
    private JLabel picLabel;
```
- **第29-43行**: LoginFrame类声明和GUI组件成员变量
  - 继承自`JFrame`，是一个窗口类
  - 定义了登录界面所需的所有GUI组件
  - `usernameField`: 用户名输入框
  - `passwordField`: 密码输入框（隐藏输入）
  - `LoginButton`: 登录按钮
  - `ExitButton`: 退出按钮
  - `serverAddressField`: 服务器地址输入框
  - `portField`: 端口号输入框
  - 各种标签用于显示提示文字

### 构造函数

```java
public LoginFrame(Client client) {
    super("简易聊天室");
    initComponents();
    setupEventListeners(client);
    setupFrame();
}
```
- **第44-49行**: LoginFrame构造函数
  - 调用父类构造函数设置窗口标题为"简易聊天室"
  - `initComponents()`: 初始化GUI组件
  - `setupEventListeners(client)`: 设置事件监听器
  - `setupFrame()`: 设置窗口属性

### 组件初始化方法

```java
private void initComponents() {
    loginPanel = new JPanel();
    usernameField = new JTextField();
    passwordField = new JPasswordField();
    LoginButton = new JButton("登录");
    ExitButton = new JButton("退出");
    serverAddressField = new JTextField("localhost");
    portField = new JTextField("8888");
    usernameLabel = new JLabel("用户名:");
    passwordLabel = new JLabel("密码:");
    serverAddressLabel = new JLabel("服务器地址:");
    portLabel = new JLabel("端口:");
    testButton = new JButton("测试连接");
    picLabel = new JLabel();
```
- **第51-64行**: 初始化所有GUI组件
  - 创建各种输入框、按钮、标签
  - 设置默认值：服务器地址为"localhost"，端口为"8888"

### 布局设置

```java
// 设置布局
GroupLayout layout = new GroupLayout(loginPanel);
loginPanel.setLayout(layout);
layout.setHorizontalGroup(
    layout.createParallelGroup(GroupLayout.Alignment.LEADING)
        .addGroup(layout.createSequentialGroup()
            .addGap(50, 50, 50)
            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                .addGroup(layout.createSequentialGroup()
                    .addComponent(serverAddressLabel)
                    .addComponent(serverAddressField, 150, 150, 150))
                .addGroup(layout.createSequentialGroup()
                    .addComponent(portLabel)
                    .addComponent(portField, 150, 150, 150)
                    .addComponent(testButton))
                .addGroup(layout.createSequentialGroup()
                    .addComponent(usernameLabel)
                    .addComponent(usernameField, 150, 150, 150))
                .addGroup(layout.createSequentialGroup()
                    .addComponent(passwordLabel)
                    .addComponent(passwordField, 150, 150, 150))
                .addGroup(layout.createSequentialGroup()
                    .addComponent(LoginButton)
                    .addComponent(ExitButton)))
            .addGap(50, 50, 50))
);
```
- **第66-90行**: 设置水平布局
  - 使用`GroupLayout`布局管理器
  - 创建并行组和顺序组来排列组件
  - 设置组件间距和大小
  - 输入框宽度固定为150像素

```java
layout.setVerticalGroup(
    layout.createSequentialGroup()
        .addGap(30, 30, 30)
        .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
            .addComponent(serverAddressLabel)
            .addComponent(serverAddressField))
        .addGap(10, 10, 10)
        .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
            .addComponent(portLabel)
            .addComponent(portField)
            .addComponent(testButton))
        .addGap(20, 20, 20)
        .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
            .addComponent(usernameLabel)
            .addComponent(usernameField))
        .addGap(10, 10, 10)
        .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
            .addComponent(passwordLabel)
            .addComponent(passwordField))
        .addGap(20, 20, 20)
        .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
            .addComponent(LoginButton)
            .addComponent(ExitButton))
        .addGap(30, 30, 30)
);
```
- **第92-116行**: 设置垂直布局
  - 从上到下依次排列：服务器地址、端口、用户名、密码、按钮
  - 设置组件间的垂直间距
  - 使用`BASELINE`对齐方式使同行组件基线对齐

### 事件监听器设置

```java
private void setupEventListeners(final Client client) {
    LoginButton.addActionListener(new ActionListener() {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (client.connect(serverAddressField.getText(), portField.getText(), false)) {
                String username = usernameField.getText();
                String password = new String(passwordField.getPassword());
                String result = client.authenticate(username, password);
                if (result == null) {
                    dispose();
                    client.loop();
                } else
                    JOptionPane.showMessageDialog(LoginFrame.this, result, "登录失败", JOptionPane.ERROR_MESSAGE);
            } else
                JOptionPane.showMessageDialog(LoginFrame.this, "服务器连接错误", "连接错误", JOptionPane.ERROR_MESSAGE);
        }
    });
```
- **第119-135行**: 登录按钮事件处理
  - 获取服务器地址和端口，尝试连接服务器
  - 如果连接成功，获取用户名和密码进行身份验证
  - 验证成功则关闭登录窗口，启动主界面
  - 验证失败则显示错误消息对话框

```java
ExitButton.addActionListener(new ActionListener() {
    @Override
    public void actionPerformed(ActionEvent e) {
        System.exit(0);
    }
});
```
- **第137-142行**: 退出按钮事件处理
  - 直接调用`System.exit(0)`退出程序

```java
testButton.addActionListener(new ActionListener() {
    @Override
    public void actionPerformed(ActionEvent e) {
        if (client.connect(serverAddressField.getText(), portField.getText(), true))
            JOptionPane.showMessageDialog(LoginFrame.this, "服务器连接成功，请输入账号密码", "测试连接成功", JOptionPane.INFORMATION_MESSAGE);
        else
            JOptionPane.showMessageDialog(LoginFrame.this, "服务器连接错误", "连接错误", JOptionPane.ERROR_MESSAGE);
    }
});
```
- **第144-152行**: 测试连接按钮事件处理
  - 测试与服务器的连接（第三个参数为true表示测试模式）
  - 显示连接结果的消息对话框

### 窗口设置方法

```java
private void setupFrame() {
    try {
        setIconImage(new ImageIcon(ChatRoomShared.Constants.ICON_FILE).getImage());
    } catch (Exception e) {
        // 图标加载失败时忽略
    }
    setContentPane(loginPanel);
    setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
    pack();
    setLocationRelativeTo(null);
    setVisible(true);
}
```
- **第155-166行**: 设置窗口属性
  - 尝试设置窗口图标（如果图标文件存在）
  - 设置内容面板为loginPanel
  - 设置关闭操作为退出程序
  - `pack()`: 自动调整窗口大小以适应组件
  - `setLocationRelativeTo(null)`: 窗口居中显示
  - `setVisible(true)`: 显示窗口

## ClientView 客户端主界面类

### 类声明和成员变量

```java
public static class ClientView extends JFrame {
    private final Client client;
    private final DefaultListModel<String> onlineUserListModel = new DefaultListModel<String>();
    private JPanel chatRoom;
    private JTextField inputField;
    private JButton sendButton;
    private JLabel currentUsername;
    private JComboBox<String> anonymousSelect;
    private JList<String> onlineUserList;
    private JButton logoutButton;
    private JTextPane chatContent;
    private JScrollPane onlineUserPanel;
    private JScrollPane messagePanel;
    private JPanel userInfoPanel;
    private JLabel onlineUserLabel;
    private JPanel chatPanel;
    private JLabel chatLabel;
    private JLabel inputLabel;
    private JLabel authorLabel;
```
- **第170-189行**: ClientView类声明和成员变量
  - 继承自`JFrame`，是主聊天界面窗口
  - `client`: 客户端实例的引用
  - `onlineUserListModel`: 在线用户列表的数据模型
  - 定义了聊天界面所需的所有GUI组件
  - `chatContent`: 使用`JTextPane`显示富文本聊天内容
  - `onlineUserList`: 显示在线用户列表
  - `anonymousSelect`: 匿名模式选择下拉框

### 构造函数

```java
public ClientView(Client client) {
    super("简易聊天室-主界面");
    this.client = client;
    initComponents();
    setupEventListeners();
    setupFrame();
}
```
- **第190-196行**: ClientView构造函数
  - 设置窗口标题为"简易聊天室-主界面"
  - 保存客户端实例引用
  - 依次调用初始化方法

### 组件初始化方法

```java
private void initComponents() {
    chatRoom = new JPanel();
    inputField = new JTextField();
    sendButton = new JButton("发送");
    currentUsername = new JLabel("当前用户: " + client.getUsername());
    anonymousSelect = new JComboBox<String>(new String[]{"实名", "匿名"});
    onlineUserList = new JList<String>(onlineUserListModel);
    logoutButton = new JButton("退出");
    chatContent = new JTextPane();
    chatContent.setEditable(false);

    onlineUserPanel = new JScrollPane(onlineUserList);
    messagePanel = new JScrollPane(chatContent);

    userInfoPanel = new JPanel();
    onlineUserLabel = new JLabel("在线用户");
    chatPanel = new JPanel();
    chatLabel = new JLabel("聊天内容");
    inputLabel = new JLabel("输入:");
    authorLabel = new JLabel("作者信息");
```
- **第198-217行**: 初始化主界面组件
  - 创建主面板和各种控件
  - 设置当前用户名标签显示登录用户名
  - 创建匿名模式选择下拉框，包含"实名"和"匿名"选项
  - 设置聊天内容面板为不可编辑
  - 为列表和文本面板添加滚动条

### 主界面布局设置

```java
// 设置布局
GroupLayout layout = new GroupLayout(chatRoom);
chatRoom.setLayout(layout);
layout.setHorizontalGroup(
    layout.createParallelGroup(GroupLayout.Alignment.LEADING)
        .addGroup(layout.createSequentialGroup()
            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                .addComponent(chatLabel)
                .addComponent(messagePanel, 500, 500, 500)
                .addGroup(layout.createSequentialGroup()
                    .addComponent(inputLabel)
                    .addComponent(inputField, 400, 400, 400)
                    .addComponent(sendButton)))
            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                .addComponent(currentUsername)
                .addComponent(anonymousSelect)
                .addComponent(onlineUserLabel)
                .addComponent(onlineUserPanel, 150, 150, 150)
                .addComponent(logoutButton)))
);
```
- **第219-237行**: 设置水平布局
  - 左侧：聊天内容区域（500像素宽）和输入区域
  - 右侧：用户信息、匿名选择、在线用户列表（150像素宽）和退出按钮
  - 使用并行组和顺序组来组织布局

```java
layout.setVerticalGroup(
    layout.createSequentialGroup()
        .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
            .addComponent(chatLabel)
            .addComponent(currentUsername))
        .addGroup(layout.createParallelGroup(GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addComponent(messagePanel, 400, 400, 400)
                .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                    .addComponent(inputLabel)
                    .addComponent(inputField)
                    .addComponent(sendButton)))
            .addGroup(layout.createSequentialGroup()
                .addComponent(anonymousSelect)
                .addComponent(onlineUserLabel)
                .addComponent(onlineUserPanel, 300, 300, 300)
                .addComponent(logoutButton)))
);
```
- **第239-256行**: 设置垂直布局
  - 顶部：聊天标签和用户名标签
  - 中间：聊天内容区域（400像素高）和右侧控制区域
  - 底部：输入框和发送按钮
  - 在线用户列表高度为300像素

### 事件监听器设置

```java
private void setupEventListeners() {
    sendButton.addActionListener(new ActionListener() {
        @Override
        public void actionPerformed(ActionEvent e) {
            send();
        }
    });
    inputField.addActionListener(new ActionListener() {
        @Override
        public void actionPerformed(ActionEvent e) {
            send();
        }
    });
```
- **第259-271行**: 发送消息事件处理
  - 发送按钮点击和输入框回车都会触发`send()`方法
  - 实现了两种发送消息的方式

```java
anonymousSelect.addActionListener(new ActionListener() {
    @Override
    public void actionPerformed(ActionEvent e) {
        client.setAnonymous(anonymousSelect.getSelectedIndex() == 1);
    }
});
```
- **第273-278行**: 匿名模式选择事件处理
  - 当下拉框选择改变时，更新客户端的匿名状态
  - 索引1表示"匿名"模式，索引0表示"实名"模式

```java
addWindowListener(new WindowAdapter() {
    @Override
    public void windowClosing(WindowEvent e) {
        client.handleCommand("quit");
        client.stop();
    }
});
```
- **第280-286行**: 窗口关闭事件处理
  - 当用户点击窗口关闭按钮时，发送退出命令并停止客户端

```java
onlineUserList.addListSelectionListener(new ListSelectionListener() {
    @Override
    public void valueChanged(ListSelectionEvent e) {
        if (!onlineUserList.isSelectionEmpty()) {
            String selectedItem = onlineUserList.getSelectedValue();
            if (inputField.getText().startsWith("@"))
                JOptionPane.showMessageDialog(chatRoom, "输入框已经以 @ 开头", "操作错误", JOptionPane.ERROR_MESSAGE);
            else
                inputField.setText("@" + selectedItem + " " + inputField.getText());
            onlineUserList.clearSelection();
        }
    }
});
```
- **第288-300行**: 在线用户列表选择事件处理
  - 当用户点击在线用户列表中的用户名时
  - 检查输入框是否已经以"@"开头（避免重复添加）
  - 在输入框前添加"@用户名 "来实现私聊功能
  - 清除列表选择状态

```java
logoutButton.addActionListener(new ActionListener() {
    @Override
    public void actionPerformed(ActionEvent e) {
        client.handleCommand("quit");
        client.stop();
    }
});
```
- **第302-308行**: 退出按钮事件处理
  - 发送退出命令并停止客户端

### 窗口设置和工具方法

```java
private void setupFrame() {
    try {
        setIconImage(new ImageIcon(ChatRoomShared.Constants.ICON_FILE).getImage());
    } catch (Exception e) {
        // 图标加载失败时忽略
    }
    setContentPane(chatRoom);
    setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
    pack();
    setLocationRelativeTo(null);
    setVisible(true);
}
```
- **第311-322行**: 设置主界面窗口属性
  - 设置窗口图标
  - 设置内容面板、关闭操作、自动调整大小、居中显示

```java
private void send() {
    String content = inputField.getText().trim();
    client.handleUserInput(content);
    inputField.setText("");
}
```
- **第324-328行**: 发送消息方法
  - 获取输入框内容并去除首尾空格
  - 调用客户端的用户输入处理方法
  - 清空输入框

```java
public void setAnonymous(boolean isAnonymous) {
    anonymousSelect.setSelectedIndex(isAnonymous ? 1 : 0);
}
```
- **第330-332行**: 设置匿名模式方法
  - 根据布尔值设置下拉框的选中项

```java
public void addUser(String username) {
    onlineUserListModel.addElement(username);
    onlineUserList.setModel(onlineUserListModel);
}

public void delUser(String username) {
    onlineUserListModel.removeElement(username);
    onlineUserList.setModel(onlineUserListModel);
}
```
- **第334-342行**: 用户列表管理方法
  - `addUser()`: 添加用户到在线用户列表
  - `delUser()`: 从在线用户列表中移除用户
  - 更新列表模型后重新设置到列表组件

```java
public void setUserList(List<String> users) {
    onlineUserListModel.clear();
    for (String username : users)
        onlineUserListModel.addElement(username);
}
```
- **第344-348行**: 设置完整用户列表方法
  - 清空当前列表模型
  - 遍历用户列表，逐个添加到模型中

### 消息显示方法

```java
public void addTextMessage(ChatRoomShared.Message message, String username) {
    String prefix = ChatRoomShared.Messages.getMessagePrefix(message, username);
    String text = ChatRoomShared.Messages.getMessageContent(message);
    StyledDocument doc = chatContent.getStyledDocument();
    SimpleAttributeSet prefixStyle = new SimpleAttributeSet();
    StyleConstants.setBold(prefixStyle, true);
    StyleConstants.setFontFamily(prefixStyle, "Consolas");
    StyleConstants.setFontSize(prefixStyle, 20);
    if (message instanceof ChatRoomShared.SystemMessage)
        StyleConstants.setForeground(prefixStyle, Color.BLUE);
    if (message instanceof ChatRoomShared.UserPrivateMessage)
        StyleConstants.setForeground(prefixStyle, new Color(118, 3, 137));
```
- **第350-361行**: 消息显示方法开始
  - 获取消息前缀（时间戳和发送者信息）
  - 获取消息内容文本
  - 获取文档对象用于富文本操作
  - 设置前缀样式：粗体、Consolas字体、20号字
  - 系统消息显示为蓝色
  - 私聊消息显示为紫色

```java
    SimpleAttributeSet textStyle = new SimpleAttributeSet();
    StyleConstants.setFontFamily(textStyle, "SansSerif");
    StyleConstants.setFontSize(textStyle, 20);
    if (message instanceof ChatRoomShared.UserMessage) {
        ChatRoomShared.UserMessage um = (ChatRoomShared.UserMessage) message;
        if (Objects.equals(um.getSender(), username)) {
            StyleConstants.setAlignment(prefixStyle, StyleConstants.ALIGN_RIGHT);
            StyleConstants.setAlignment(textStyle, StyleConstants.ALIGN_RIGHT);
        } else {
            StyleConstants.setAlignment(prefixStyle, StyleConstants.ALIGN_LEFT);
            StyleConstants.setAlignment(textStyle, StyleConstants.ALIGN_LEFT);
        }
    } else {
        StyleConstants.setAlignment(prefixStyle, StyleConstants.ALIGN_LEFT);
        StyleConstants.setAlignment(textStyle, StyleConstants.ALIGN_LEFT);
    }
```
- **第362-377行**: 设置文本样式和对齐方式
  - 设置消息内容样式：SansSerif字体、20号字
  - 如果是用户消息且发送者是当前用户，则右对齐
  - 其他消息左对齐
  - 系统消息统一左对齐

```java
    try {
        doc.insertString(doc.getLength(), prefix + "\n", prefixStyle);
        doc.setParagraphAttributes(doc.getLength() - prefix.length() - 1, prefix.length() + 1, prefixStyle, false);
        doc.insertString(doc.getLength(), "\t" + text + "\n" + "\n", textStyle);
        doc.setParagraphAttributes(doc.getLength() - text.length() - 3, text.length() + 3, textStyle, false);
    } catch (BadLocationException ex) {
        System.err.println("Error: " + ex.getMessage());
    }
    if (message instanceof ChatRoomShared.UserMessage) {
        ChatRoomShared.UserMessage um = (ChatRoomShared.UserMessage) message;
        if (Objects.equals(um.getSender(), username))
            chatContent.setCaretPosition(chatContent.getDocument().getLength());
    }
}
```
- **第378-391行**: 插入文本到文档
  - 插入消息前缀并应用样式
  - 插入消息内容（带制表符缩进）并应用样式
  - 设置段落属性
  - 如果是当前用户发送的消息，将光标移动到文档末尾

```java
public void displayMessage(String message) {
    StyledDocument doc = chatContent.getStyledDocument();
    SimpleAttributeSet textStyle = new SimpleAttributeSet();
    StyleConstants.setFontFamily(textStyle, "Consolas");
    StyleConstants.setFontSize(textStyle, 20);
    StyleConstants.setForeground(textStyle, Color.MAGENTA);
    StyleConstants.setAlignment(textStyle, StyleConstants.ALIGN_CENTER);
    try {
        doc.insertString(doc.getLength(), message + "\n", textStyle);
        doc.setParagraphAttributes(doc.getLength() - message.length() - 1, message.length() + 1, textStyle, false);
    } catch (BadLocationException ex) {
        System.err.println("Error: " + ex.getMessage());
    }
    chatContent.setCaretPosition(chatContent.getDocument().getLength());
}
```
- **第393-407行**: 显示系统提示消息方法
  - 用于显示客户端本地的提示信息
  - 设置样式：Consolas字体、20号字、洋红色、居中对齐
  - 插入消息并移动光标到末尾

## Client 客户端主类

### 类声明和成员变量

```java
public static class Client {
    private Socket socket;
    private ObjectInputStream in;
    private ObjectOutputStream out;
    private String username;
    private boolean isAnonymous = false;
    private ClientView clientView;
```
- **第411-417行**: Client类声明和成员变量
  - `socket`: 网络连接套接字
  - `in`, `out`: 对象输入输出流，用于与服务器通信
  - `username`: 当前登录用户名
  - `isAnonymous`: 是否处于匿名模式
  - `clientView`: 主界面窗口引用

### 主方法

```java
public static void main(String[] args) {
    SwingUtilities.invokeLater(new Runnable() {
        @Override
        public void run() {
            new LoginFrame(new Client());
        }
    });
}
```
- **第419-426行**: 程序入口点
  - 使用`SwingUtilities.invokeLater()`确保GUI在事件分发线程中创建
  - 创建Client实例并传递给LoginFrame

### 网络通信方法

```java
public void sendSystemRequest(ChatRoomShared.MessageContent content) {
    try {
        out.writeObject(new ChatRoomShared.SystemRequest(username, content));
    } catch (IOException e) {
        System.err.println("Error sending system request: " + e.getMessage());
    }
}
```
- **第428-434行**: 发送系统请求方法
  - 创建SystemRequest对象并通过输出流发送给服务器
  - 包含当前用户名和消息内容
  - 捕获并处理IO异常

```java
public String authenticate(String username, String password) {
    this.username = username;
    sendSystemRequest(new ChatRoomShared.TextMessageContent(username));
    sendSystemRequest(new ChatRoomShared.TextMessageContent(password));
    try {
        ChatRoomShared.SystemReply serverMessage = (ChatRoomShared.SystemReply) in.readObject();
        String result = (String) serverMessage.getContent().getContent();
        if (Objects.equals(result, ChatRoomShared.SystemReply.LOGIN_SUCCESS)) {
            return null;
        } else
            return result;
    } catch (IOException e) {
        throw new RuntimeException(e);
    } catch (ClassNotFoundException e) {
        throw new RuntimeException(e);
    }
}
```
- **第436-452行**: 身份验证方法
  - 保存用户名
  - 分别发送用户名和密码到服务器
  - 读取服务器返回的验证结果
  - 如果返回LOGIN_SUCCESS则验证成功（返回null）
  - 否则返回错误信息字符串

### 命令处理方法

```java
public void handleCommand(String command) {
    switch (command.toLowerCase()) {
        case "list":
            sendSystemRequest(new ChatRoomShared.TextMessageContent("list"));
            break;
        case "quit":
            sendSystemRequest(new ChatRoomShared.TextMessageContent("quit"));
            stop();
            System.exit(0);
            break;
        case "showanonymous":
            if (clientView != null) {
                clientView.displayMessage("Current chat mode: " + (isAnonymous ? "Anonymous" : "Named"));
            }
            break;
        case "anonymous":
            setAnonymous(!isAnonymous);
            if (clientView != null) {
                clientView.displayMessage("Chat mode changed to: " + (isAnonymous ? "Anonymous" : "Named"));
            }
            break;
        default:
            if (clientView != null) {
                clientView.displayMessage("Unknown command. Available commands: list, quit, showanonymous, anonymous");
            }
    }
}
```
- **第454-480行**: 处理客户端命令
  - `list`: 请求在线用户列表
  - `quit`: 发送退出请求，停止客户端并退出程序
  - `showanonymous`: 显示当前聊天模式（匿名/实名）
  - `anonymous`: 切换匿名模式
  - 未知命令显示帮助信息

```java
public void setAnonymous(boolean anonymous) {
    isAnonymous = anonymous;
    if (clientView != null) {
        clientView.setAnonymous(anonymous);
    }
}
```
- **第482-487行**: 设置匿名模式方法
  - 更新内部匿名状态
  - 同步更新界面的匿名选择控件

### 连接管理方法

```java
public boolean connect(String address, String port, boolean isTest) {
    try {
        socket = new Socket(address, Integer.parseInt(port));
        out = new ObjectOutputStream(socket.getOutputStream());
        in = new ObjectInputStream(socket.getInputStream());
        if (isTest) {
            in.close();
            in = null;
            out.close();
            out = null;
            socket.close();
            socket = null;
        }
    } catch (IOException e) {
        System.err.println("Error connecting to " + address + ":" + port);
        return false;
    }
    return true;
}
```
- **第489-507行**: 连接服务器方法
  - 创建Socket连接到指定地址和端口
  - 创建对象输入输出流
  - 如果是测试模式，立即关闭连接（只测试连通性）
  - 连接失败返回false，成功返回true

```java
public void loop() {
    SwingWorker<Void, ChatRoomShared.Message> worker = new SwingWorker<Void, ChatRoomShared.Message>() {
        @Override
        protected Void doInBackground() {
            try {
                ChatRoomShared.Message serverMessage;
                while ((serverMessage = (ChatRoomShared.Message) in.readObject()) != null)
                    publish(serverMessage);
            } catch (Exception ignored) {
                if (clientView != null) {
                    JOptionPane.showMessageDialog(clientView, "服务器连接错误", "服务器连接错误", JOptionPane.ERROR_MESSAGE);
                }
                stop();
            }
            return null;
        }

        @Override
        protected void process(List<ChatRoomShared.Message> chunks) {
            for (ChatRoomShared.Message message : chunks)
                handleServerMessage(message);
        }
    };
    worker.execute();
    clientView = new ClientView(this);
    clientView.setAnonymous(isAnonymous);
    sendSystemRequest(new ChatRoomShared.TextMessageContent("list"));
}
```
- **第509-536行**: 主循环方法
  - 使用SwingWorker在后台线程处理服务器消息
  - `doInBackground()`: 持续读取服务器消息并发布
  - `process()`: 在EDT线程中处理接收到的消息
  - 创建主界面窗口
  - 设置匿名模式状态
  - 请求在线用户列表

```java
public void stop() {
    try {
        if (in != null) in.close();
        if (out != null) out.close();
        if (socket != null) socket.close();
    } catch (IOException e) {
        System.err.println("Error closing client connection: " + e.getMessage());
    }
    System.exit(0);
}
```
- **第538-547行**: 停止客户端方法
  - 关闭输入输出流和Socket连接
  - 退出程序

### 用户输入处理方法

```java
public void handleUserInput(String content) {
    if (content.isEmpty()) {
        if (clientView != null) {
            JOptionPane.showMessageDialog(clientView, "输入不能为空", "发送错误", JOptionPane.ERROR_MESSAGE);
        }
        return;
    }
    if (content.startsWith("@@"))
        handleCommand(content.substring(2));
    else
        handleUserMessage(content);
}
```
- **第549-560行**: 处理用户输入方法
  - 检查输入是否为空
  - 如果以"@@"开头，则作为命令处理
  - 否则作为普通消息处理

```java
public void handleUserMessage(String content) {
    ChatRoomShared.Message message;
    if (content.startsWith("@")) {
        int spaceIndex = content.indexOf(' ');
        if (spaceIndex != -1) {
            String recipient = content.substring(1, spaceIndex);
            if (recipient.equals(username)) {
                if (clientView != null) {
                    JOptionPane.showMessageDialog(clientView, "不能给自己发私信", "发送错误", JOptionPane.ERROR_MESSAGE);
                }
                return;
            }
            String privateMessage = content.substring(spaceIndex + 1);
            message = new ChatRoomShared.UserPrivateMessage(username, isAnonymous, recipient, new ChatRoomShared.TextMessageContent(privateMessage));
        } else {
            if (clientView != null) {
                JOptionPane.showMessageDialog(clientView, "消息不能为空", "发送错误", JOptionPane.ERROR_MESSAGE);
            }
            return;
        }
    } else
        message = new ChatRoomShared.UserBroadcastMessage(username, isAnonymous, new ChatRoomShared.TextMessageContent(content));
    try {
        out.writeObject(message);
    } catch (IOException e) {
        System.err.println("Error sending message: " + e.getMessage());
    }
}
```
- **第562-589行**: 处理用户消息方法
  - 如果以"@"开头，解析为私聊消息
  - 提取接收者用户名和消息内容
  - 检查不能给自己发私信
  - 创建UserPrivateMessage对象
  - 否则创建UserBroadcastMessage对象（群聊）
  - 通过输出流发送消息

### 服务器消息处理方法

```java
public void handleServerMessage(ChatRoomShared.Message message) {
    if (message instanceof ChatRoomShared.SystemBroadcast) {
        ChatRoomShared.SystemBroadcast sb = (ChatRoomShared.SystemBroadcast) message;
        if (Objects.equals(sb.getBroadcastType(), "join"))
            clientView.addUser(sb.getUsername());
        if (Objects.equals(sb.getBroadcastType(), "left"))
            clientView.delUser(sb.getUsername());
    }
    if (message instanceof ChatRoomShared.SystemUserList) {
        ChatRoomShared.SystemUserList sul = (ChatRoomShared.SystemUserList) message;
        clientView.setUserList(sul.getUsers());
        return;
    }
    clientView.addTextMessage(message, username);
}
```
- **第591-605行**: 处理服务器消息方法
  - 如果是SystemBroadcast：
    - "join"类型：添加用户到在线列表
    - "left"类型：从在线列表移除用户
  - 如果是SystemUserList：更新完整的在线用户列表
  - 其他消息：添加到聊天内容显示区域

```java
public String getUsername() {
    return username;
}
```
- **第607-609行**: 获取用户名方法
  - 返回当前登录的用户名

## 总结

ChatRoomClient.java文件实现了完整的聊天室客户端功能：

1. **LoginFrame**: 提供用户登录界面，支持服务器连接测试
2. **ClientView**: 主聊天界面，支持群聊、私聊、匿名模式等功能
3. **Client**: 核心客户端逻辑，处理网络通信、消息处理、用户交互等

整个设计采用了MVC模式，界面与逻辑分离，使用Swing构建GUI，通过Socket进行网络通信，支持对象序列化传输消息。
