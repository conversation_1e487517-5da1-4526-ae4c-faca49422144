================================================================================
                            简易聊天室系统项目说明文档
================================================================================

项目名称：简易聊天室系统
开发语言：Java 8
界面框架：Swing
网络通信：Socket + 对象序列化
项目结构：客户端-服务器架构

================================================================================
一、设计思路
================================================================================

1. 整体架构设计
   本项目采用经典的客户端-服务器(C/S)架构，通过Socket网络通信实现多用户实时聊天。
   设计核心思想是模块化和可扩展性，将功能按职责分离到不同的类中。

2. 通信协议设计
   使用Java对象序列化作为通信协议，定义了完整的消息类型体系，支持：
   - 用户身份验证
   - 群聊消息
   - 私聊消息
   - 系统通知
   - 匿名聊天

3. 用户体验设计
   - 现代化的图形界面，使用统一的颜色主题
   - 直观的操作流程：登录 -> 聊天 -> 退出
   - 实时的在线用户列表和消息显示
   - 支持匿名模式和私聊功能

4. 安全性考虑
   - 用户身份验证机制
   - 防止重复登录
   - 输入验证和错误处理

================================================================================
二、项目文件结构
================================================================================

src/com/chatroom/
├── ChatRoomShared.java     # 共享组件和消息定义
├── ChatRoomClient.java     # 客户端程序
└── ChatRoomServer.java     # 服务器端程序

resources/
├── users.txt              # 用户账户数据
└── icon.png              # 应用程序图标

logs/
└── chat.log              # 服务器运行日志

================================================================================
三、核心类功能说明
================================================================================

【ChatRoomShared.java - 共享组件】

1. Constants类
   功能：定义系统常量
   - PORT: 服务器监听端口(8888)
   - USER_FILE: 用户数据文件路径
   - LOG_FILE: 日志文件路径
   - ICON_FILE: 应用图标路径

2. NetworkUtil类
   功能：网络工具方法
   - getIpAddress(): 从Socket获取客户端IP地址

3. 消息类体系
   - Message: 所有消息的抽象基类
   - MessageContent: 消息内容接口
   - TextMessageContent: 文本消息内容实现
   - UserMessage: 用户消息基类
   - SystemMessage: 系统消息基类
   
   具体消息类型：
   - UserBroadcastMessage: 用户群聊消息
   - UserPrivateMessage: 用户私聊消息
   - SystemReply: 系统回复消息
   - SystemRequest: 系统请求消息
   - SystemBroadcast: 系统广播消息
   - SystemUserList: 在线用户列表消息

4. Messages工具类
   功能：消息格式化工具
   - getMessagePrefix(): 生成消息时间戳和发送者前缀
   - getMessageContent(): 提取消息文本内容

【ChatRoomClient.java - 客户端程序】

1. LoginFrame类
   功能：用户登录界面
   组件：用户名输入、密码输入、服务器配置、连接测试
   特性：现代化UI设计、输入验证、连接测试功能

2. ClientView类
   功能：聊天室主界面
   组件：聊天内容显示、消息输入、在线用户列表、匿名模式选择
   特性：富文本显示、私聊支持、实时用户列表更新

3. Client类
   功能：客户端核心逻辑
   职责：
   - 网络连接管理
   - 用户身份验证
   - 消息发送和接收
   - 命令处理(list, quit, anonymous等)
   - 界面更新控制

【ChatRoomServer.java - 服务器端程序】

1. UserManager类
   功能：用户账户管理
   职责：
   - 从文件加载用户数据
   - 用户身份验证
   - 用户存在性检查

2. Logger类
   功能：日志记录
   职责：
   - 格式化日志消息
   - 写入日志文件
   - 线程安全的日志记录

3. ClientHandler类
   功能：客户端连接处理
   职责：
   - 处理单个客户端连接
   - 用户身份验证流程
   - 消息转发和处理
   - 客户端命令执行

4. ServerView类
   功能：服务器管理界面
   组件：日志显示、命令输入
   特性：实时日志显示、服务器命令执行

5. Server类
   功能：服务器核心管理
   职责：
   - 监听客户端连接
   - 管理客户端列表
   - 消息广播和转发
   - 服务器生命周期管理
   - 线程池管理

================================================================================
四、类的使用说明
================================================================================

【启动服务器】
1. 运行ChatRoomServer.java的main方法
2. 服务器自动启动并显示管理界面
3. 可在命令输入框中执行服务器命令：
   - list: 查看在线用户
   - listall: 查看所有注册用户
   - quit: 关闭服务器

【启动客户端】
1. 运行ChatRoomClient.java的main方法
2. 在登录界面输入用户名和密码
3. 可选择修改服务器地址和端口
4. 点击"测试连接"验证网络连接
5. 点击"登录"进入聊天室

【聊天功能使用】
1. 群聊：直接在输入框输入消息
2. 私聊：输入"@用户名 消息内容"
3. 匿名模式：选择"匿名"后发送的消息不显示真实用户名
4. 客户端命令：输入"@@命令"执行客户端命令
   - @@list: 刷新在线用户列表
   - @@quit: 退出聊天室
   - @@anonymous: 切换匿名模式
   - @@showanonymous: 显示当前模式

【用户管理】
用户数据存储在resources/users.txt文件中，格式为"用户名:密码"
可以手动编辑此文件添加新用户或修改密码

================================================================================
五、类的关系图
================================================================================

服务器端类关系：
Server (主控制器)
├── UserManager (用户管理)
├── Logger (日志记录)  
├── ServerView (管理界面)
└── ClientHandler[] (客户端处理器数组)
    └── 使用 ChatRoomShared 中的消息类

客户端类关系：
Client (主控制器)
├── LoginFrame (登录界面)
└── ClientView (聊天界面)
    └── 使用 ChatRoomShared 中的消息类

共享组件关系：
ChatRoomShared
├── Constants (常量定义)
├── NetworkUtil (网络工具)
├── Messages (消息工具)
└── 消息类体系
    ├── Message (基类)
    ├── UserMessage (用户消息)
    │   ├── UserBroadcastMessage (群聊)
    │   └── UserPrivateMessage (私聊)
    └── SystemMessage (系统消息)
        ├── SystemReply (系统回复)
        ├── SystemRequest (系统请求)
        ├── SystemBroadcast (系统广播)
        └── SystemUserList (用户列表)

数据流向：
1. 客户端 -> 服务器：用户消息、系统请求
2. 服务器 -> 客户端：系统回复、消息转发、用户列表
3. 服务器 -> 所有客户端：系统广播、群聊消息
4. 服务器 -> 特定客户端：私聊消息

================================================================================
六、技术特点
================================================================================

1. 网络通信
   - 使用Socket进行TCP连接
   - 对象序列化实现复杂数据传输
   - 多线程处理并发连接

2. 界面设计
   - Swing GUI框架
   - GroupLayout布局管理
   - 现代化颜色主题和字体
   - 响应式用户交互

3. 并发处理
   - 服务器使用线程池管理客户端连接
   - 客户端使用SwingWorker处理后台网络通信
   - 线程安全的数据结构

4. 错误处理
   - 完善的异常捕获和处理
   - 用户友好的错误提示
   - 网络断开自动处理

5. 可扩展性
   - 模块化的类设计
   - 清晰的接口定义
   - 易于添加新的消息类型和功能

================================================================================
七、使用注意事项
================================================================================

1. 系统要求
   - Java 8或更高版本
   - 网络连接正常
   - 确保端口8888未被占用

2. 部署说明
   - 先启动服务器，再启动客户端
   - 确保resources目录下有users.txt文件
   - logs目录会自动创建

3. 故障排除
   - 连接失败：检查服务器是否启动、网络是否通畅
   - 登录失败：检查用户名密码是否正确
   - 界面异常：检查Java版本和Swing支持

4. 性能考虑
   - 服务器支持的并发连接数取决于系统资源
   - 大量消息时可能影响界面响应速度
   - 建议定期清理日志文件

================================================================================
文档结束
================================================================================
