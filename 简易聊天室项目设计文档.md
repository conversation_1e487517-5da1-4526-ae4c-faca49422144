# 简易聊天室项目设计文档

## 一、设计内容

### 1.1 设计内容简介

设计并实现一个基于图形界面的C/S结构的简易聊天室程序。该程序采用Java语言开发，使用Swing构建图形用户界面，通过Socket实现网络通信，支持多用户同时在线聊天、私聊、匿名聊天等功能。

**主要特点：**
- 基于C/S架构的网络聊天应用
- 图形化用户界面，操作简便
- 支持多用户并发聊天（至少5个用户）
- 完整的用户认证和管理系统
- 丰富的聊天功能（群聊、私聊、匿名）
- 完善的日志记录和管理功能

### 1.2 需求分析

#### 1.2.1 服务器端功能需求

| 功能模块 | 功能描述 | 具体要求 |
|---------|---------|---------|
| **用户加载功能** | 启动时读取用户文件，管理用户账户 | 从users.txt文件读取至少10个用户的用户名和密码信息 |
| **命令功能** | 提供服务器管理命令接口 | 支持list（列出在线用户）、listall（列出全部用户）、quit（退出系统）命令 |
| **端口侦听功能** | 在固定端口监听客户端连接 | 在8888端口持续监听，支持多客户端并发连接 |
| **用户登录验证功能** | 验证客户端用户身份 | 验证用户名密码，支持重复验证直到成功或断开连接 |
| **聊天功能** | 转发和管理聊天消息 | 支持至少5个客户端同时聊天，转发群聊和私聊消息 |
| **匿名功能** | 支持匿名聊天模式 | 用户可选择匿名发送消息，隐藏真实身份 |
| **日志功能** | 记录用户行为和系统事件 | 记录登录成功/失败信息（含IP、时间）、退出时间等 |

#### 1.2.2 客户端功能需求

| 功能模块 | 功能描述 | 具体要求 |
|---------|---------|---------|
| **连接服务器功能** | 建立与服务器的网络连接 | 支持服务器地址和端口配置，提供连接测试功能 |
| **登录功能** | 用户身份验证和登录流程 | 用户名密码验证，支持重新输入直到成功或退出 |
| **消息展示功能** | 显示聊天内容和用户信息 | 显示所有非私聊信息和发给自己的私聊信息，支持富文本显示 |
| **用户输入判定功能** | 识别和处理不同类型的用户输入 | 普通字符串（群聊）、@+用户名（私聊）、@@+命令（系统命令） |
| **命令功能** | 执行客户端系统命令 | list、quit、showanonymous、anonymous等命令 |

## 二、项目文件结构

```
Classroom_1/
├── src/
│   └── com/
│       └── chatroom/
│           ├── ChatRoomClient.java      # 客户端主程序
│           ├── ChatRoomServer.java      # 服务器端主程序
│           └── ChatRoomShared.java      # 共享组件和消息类
├── resources/
│   ├── users.txt                        # 用户账户数据文件
│   ├── icon.png                         # 应用程序图标
│   └── totoro.gif                       # 界面装饰图片
├── logs/
│   └── chat.log                         # 系统运行日志
├── documents/                           # 项目文档目录
│   ├── ChatRoomClient详细解释.md
│   ├── ChatRoomServer详细解释.md
│   ├── ChatRoomShared详细解释.md
│   ├── 功能需求表格.md
│   ├── 布局修复说明.md
│   └── 登录界面布局修复说明.md
├── out/                                 # 编译输出目录
└── 项目要求.txt                         # 项目需求文档
```

## 三、核心类功能说明

### 3.1 ChatRoomShared.java - 共享组件

**主要职责：** 定义客户端和服务器端共同使用的消息类、常量、工具类等。

#### 核心组件：

1. **Constants类**
   - 定义系统常量：端口号(8888)、文件路径等
   - 统一管理配置信息

2. **NetworkUtil类**
   - 提供网络工具方法
   - getIpAddress(): 从Socket获取客户端IP地址

3. **消息类体系**
   ```
   Message (抽象基类)
   ├── UserMessage (用户消息基类)
   │   ├── UserBroadcastMessage (群聊消息)
   │   └── UserPrivateMessage (私聊消息)
   └── SystemMessage (系统消息基类)
       ├── SystemReply (系统回复)
       ├── SystemRequest (系统请求)
       ├── SystemBroadcast (系统广播)
       └── SystemUserList (用户列表)
   ```

4. **Messages工具类**
   - getMessagePrefix(): 生成消息时间戳和发送者前缀
   - getMessageContent(): 提取消息文本内容

### 3.2 ChatRoomServer.java - 服务器端

**主要职责：** 处理客户端连接、用户认证、消息转发、系统管理等服务器端功能。

#### 核心组件：

1. **UserManager类**
   - 管理用户账户信息
   - 从users.txt文件加载用户数据
   - 提供用户身份验证功能

2. **Logger类**
   - 记录系统运行日志
   - 支持时间戳格式化
   - 写入chat.log文件

3. **ClientHandler类**
   - 处理单个客户端连接（多线程）
   - 用户身份验证流程
   - 消息接收和转发
   - 客户端命令处理

4. **ServerView类**
   - 服务器管理界面
   - 显示系统日志
   - 接受管理员命令输入

5. **Server类**
   - 服务器核心管理类
   - 监听客户端连接
   - 管理客户端列表
   - 消息广播和私聊转发
   - 服务器生命周期管理

### 3.3 ChatRoomClient.java - 客户端

**主要职责：** 提供用户界面、处理用户交互、与服务器通信等客户端功能。

#### 核心组件：

1. **LoginFrame类**
   - 用户登录界面
   - 服务器连接配置
   - 连接测试功能
   - 用户身份验证

2. **ClientView类**
   - 聊天室主界面
   - 聊天内容显示（富文本）
   - 在线用户列表
   - 消息输入和发送
   - 匿名模式切换

3. **Client类**
   - 客户端核心逻辑
   - 网络连接管理
   - 消息发送和接收
   - 用户命令处理
   - 界面更新控制

## 四、使用说明

### 4.1 服务器端启动

1. **编译项目**
   ```bash
   javac -d out src/com/chatroom/*.java
   ```

2. **启动服务器**
   ```bash
   java -cp out com.chatroom.ChatRoomServer
   ```

3. **服务器管理**
   - 在服务器界面输入命令：
     - `list`: 查看在线用户
     - `listall`: 查看所有用户
     - `quit`: 退出服务器

### 4.2 客户端使用

1. **启动客户端**
   ```bash
   java -cp out com.chatroom.ChatRoomClient
   ```

2. **登录流程**
   - 输入服务器地址（默认localhost）
   - 输入端口号（默认8888）
   - 点击"测试连接"验证服务器连接
   - 输入用户名和密码
   - 点击"登录"进入聊天室

3. **聊天功能**
   - **群聊**: 直接输入消息内容
   - **私聊**: 输入`@用户名 消息内容`
   - **系统命令**: 输入`@@命令`
     - `@@list`: 查看在线用户
     - `@@anonymous`: 切换匿名模式
     - `@@showanonymous`: 显示当前聊天模式
     - `@@quit`: 退出聊天室

### 4.3 用户账户管理

用户信息存储在`resources/users.txt`文件中，格式为：
```
用户名:密码
张三:123456
李四:password
王五:12345
...
```

## 五、类的关系图

```mermaid
graph TB
    subgraph "ChatRoomShared.java"
        Constants[Constants<br/>系统常量]
        NetworkUtil[NetworkUtil<br/>网络工具]
        Message[Message<br/>消息基类]
        UserMessage[UserMessage<br/>用户消息]
        SystemMessage[SystemMessage<br/>系统消息]
        Messages[Messages<br/>消息工具]
    end
    
    subgraph "ChatRoomServer.java"
        Server[Server<br/>服务器主类]
        UserManager[UserManager<br/>用户管理]
        Logger[Logger<br/>日志记录]
        ClientHandler[ClientHandler<br/>客户端处理]
        ServerView[ServerView<br/>服务器界面]
    end
    
    subgraph "ChatRoomClient.java"
        Client[Client<br/>客户端主类]
        LoginFrame[LoginFrame<br/>登录界面]
        ClientView[ClientView<br/>聊天界面]
    end
    
    %% 依赖关系
    Server --> UserManager
    Server --> Logger
    Server --> ClientHandler
    Server --> ServerView
    ClientHandler --> Message
    ClientHandler --> NetworkUtil
    
    Client --> LoginFrame
    Client --> ClientView
    LoginFrame --> Client
    ClientView --> Client
    Client --> Message
    
    %% 通信关系
    Client -.->|Socket通信| Server
    ClientHandler -.->|消息传递| Client
```

## 六、总结与心得

### 6.1 项目总结

本项目成功实现了一个功能完整的简易聊天室系统，具有以下特点：

1. **架构设计合理**: 采用C/S架构，客户端和服务器端职责分离明确
2. **功能完整**: 支持用户认证、群聊、私聊、匿名聊天、在线用户管理等功能
3. **界面友好**: 使用Swing构建现代化的图形用户界面
4. **并发处理**: 服务器端采用多线程架构，支持多用户同时在线
5. **扩展性好**: 消息类体系设计灵活，易于添加新的消息类型和功能

### 6.2 技术心得

1. **网络编程**: 深入理解了Socket编程、对象序列化、多线程并发处理
2. **GUI设计**: 掌握了Swing组件的使用、布局管理、事件处理机制
3. **设计模式**: 应用了MVC模式、观察者模式、策略模式等设计模式
4. **异常处理**: 学会了网络异常、IO异常的处理和恢复机制
5. **代码组织**: 通过静态嵌套类合理组织代码结构，提高可维护性

### 6.3 改进方向

1. **安全性**: 可以添加密码加密、消息加密等安全机制
2. **功能扩展**: 可以添加文件传输、表情包、群组管理等功能
3. **性能优化**: 可以优化消息传输效率、内存使用等
4. **用户体验**: 可以添加消息提示音、界面主题切换等功能

### 6.4 学习收获

通过本项目的开发，不仅掌握了Java网络编程和GUI开发的核心技术，还深入理解了软件工程的设计思想和开发流程。项目从需求分析、架构设计、编码实现到测试调试的完整过程，为今后的软件开发工作奠定了坚实的基础。
