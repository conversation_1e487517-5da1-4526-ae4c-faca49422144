要求：
设计并实现一个基于图形界面的C/S结构的简易聊天室程序

程序分为服务器端和客户端两部分：
服务器端功能要求：
1、启动时首先读入一个用户文件，用户文件中保存的是全部用户（至少10个）的用户名及密码（明文保存，真实系统不会明文保存的）。然后显示一个提示符，等待输入命令。可接受的命令包括list：列出全部在线用户；listall：列出全部用户；quit：退出系统；
2、在固定端口（8000以上）侦听，等待客户端连接；
3、客户端连接成功后有一个验证用户名和密码的过程。如果用户名密码正确，要给该客户端发成功验证的提示信息，否则给客户端发送用户名或密码错误信息，并等待用户再次验证，直到验证成功或客户端断开；
4、至少支持5个客户端同时聊天；
5、转发客户端发送的聊天信息且支持匿名聊天；
6、日志功能。用文本文件记录每个用户的行为。这些行为包括每次登录成功信息（要包含IP地址，时间等）、每次登录失败信息（要包含IP地址、时间等）、每次退出聊天室时间等。

客户端功能要求：
1、启动后首先要连接到服务器端；
2、连接成功后提示用户输入用户名及密码，并将用户输入的用户名及密码发给服务器端验证。验证成功后则成功进入聊天室，否则提示用户重新输入用户名及密码，直到验证成功或用户结束程序；
3、验证成功后进入聊天室，聊天室要有提示符；
4、聊天室内要能显示所有用户的非私聊信息及给自己的私聊信息；
5、在聊天室内用户可以输入三类字符串：①普通字符串，为广播类聊天信息，所有人都可以看到；②以@+用户名开头的字符串，为私聊信息，只有@到的那个用户可以看到；③@@+命令，为系统命令；
6、系统命令有：list：列出当前在线用户；quit：退出系统；showanonymous：显示当前聊天方式是否为匿名；anonymous：切换聊天方式，即使用匿名聊天，还是实名聊天。

验收时间：2024年6月21日。具体时间地点待定。

文档要求：
写明自己的设计思路及每个类的功能。
使用说明，类的关系。

提交要求：
作业提交时间：验收后一周内。

提交文件命名要求：
请大家严格按照下述要求进行命名，否则有可能找不到你的作业从而没有成绩！
1、只提交源代码，不要提交工程； 
2、将所有源代码放到一个名字为学号的目录下；
3、将文档、源代码、用户文件等压缩成一个文件提交；
4、文档、压缩文件及邮件标题命名格式统一为：班级+学号+姓名。

问题：  
ChatRoomShared类：
1. 为什么PORT要设置为8888？能否设置为其他值？
2. import java.io.Serializable 语句的作用是什么？
3. import java.net.Socket  语句的作用是什么？
4. import java.time.LocalDateTime  语句的作用是什么？
5. import java.time.format.DateTimeFormatter  语句的作用是什么？
6. import java.util.List  语句的作用是什么？
7. import java.util.Objects  语句的作用是什么？
8. 下面内容是如何实现在一个ChatRoomShared类中再定义一个Constants类的？Java语法允许这样操作吗？这样操作的目的是什么？为什么要这样定义？
// 系统配置常量
    public static class Constants {
        public static final int PORT = 8888;  // 服务器监听端口
        public static final String USER_FILE = "resources/users.txt";  // 用户数据文件
        public static final String LOG_FILE = "logs/chat.log";  // 日志文件路径
        public static final String ICON_FILE = "resources/icon.png";  // 应用图标
    }

9. 同问题8，请你以同样的方式回答为什么该NetworkUtil类也可以这样定义
// 网络相关工具方法
    public static class NetworkUtil {
        // 从Socket连接中提取客户端IP地址
        public static String getIpAddress(Socket socket) {
            if (socket != null && socket.getInetAddress() != null) {
                return socket.getInetAddress().getHostAddress();
            }
            return "Unknown";  // 无法获取时返回默认值
        }
    }

10. 请你解释下面这个类，并且解释一下其语法规则
// 所有消息的基础类，支持网络传输
    public static abstract class Message implements Serializable {
        private static final long serialVersionUID = 1L;
        protected MessageContent content;

        public Message(MessageContent content) {
            this.content = content;
        }

        public MessageContent getContent() {
            return content;
        }

        // 子类需要实现具体的消息类型标识
        public abstract String getType();
    }

11. 同10一样，解释下面内容：
// 消息内容的通用接口
    public static interface MessageContent extends Serializable {
        Object getContent();
    }

12. 同10一样，解释下面内容：
// 纯文本消息的内容实现
    public static class TextMessageContent implements MessageContent {
        private static final long serialVersionUID = 1L;
        private final String text;

        public TextMessageContent(String text) {
            this.text = text;
        }

        @Override
        public Object getContent() {
            return text;
        }

        @Override
        public String toString() {
            return text;
        }
    }

13. 同10 解释下面内容：
// 用户发送的消息基类，包含发送者信息
    public static abstract class UserMessage extends Message {
        private static final long serialVersionUID = 1L;
        protected String sender;  // 发送者用户名
        protected boolean isAnonymous;  // 是否匿名发送

        public UserMessage(String sender, boolean isAnonymous, MessageContent content) {
            super(content);
            this.sender = sender;
            this.isAnonymous = isAnonymous;
        }

        public String getSender() {
            return sender;
        }

        public boolean isAnonymous() {
            return isAnonymous;
        }
    }

14. 同10 解释下面内容：
// 用户广播消息
    public static class UserBroadcastMessage extends UserMessage {
        private static final long serialVersionUID = 1L;

        public UserBroadcastMessage(String sender, boolean isAnonymous, MessageContent content) {
            super(sender, isAnonymous, content);
        }

        @Override
        public String getType() {
            return "USER_BROADCAST";
        }
    }

15. 同10 解释下面内容，并说明为什么要将serialVersionUID设置为1L
// 用户私聊消息
    public static class UserPrivateMessage extends UserMessage {
        private static final long serialVersionUID = 1L;
        private String receiver;

        public UserPrivateMessage(String sender, boolean isAnonymous, String receiver, MessageContent content) {
            super(sender, isAnonymous, content);
            this.receiver = receiver;
        }

        public String getReceiver() {
            return receiver;
        }

        @Override
        public String getType() {
            return "USER_PRIVATE";
        }
    }

16. 同10 解释下面内容：
// 系统回复消息
    public static class SystemReply extends SystemMessage {
        private static final long serialVersionUID = 1L;

        public static final String LOGIN_SUCCESS = "LOGIN_SUCCESS";
        public static final String PASSWORD_INCORRECT = "Password incorrect. Please try again.";
        public static final String USER_NOT_EXIST = "User does not exist. Please try again.";
        public static final String ALREADY_LOGIN = "User already logged in. Please try again.";

        public SystemReply(MessageContent content) {
            super(content);
        }

        @Override
        public String getType() {
            return "SYSTEM_REPLY";
        }
    }

17. 同10 解释下面内容：
// 系统请求消息
    public static class SystemRequest extends SystemMessage {
        private static final long serialVersionUID = 1L;
        private String username;

        public SystemRequest(String username, MessageContent content) {
            super(content);
            this.username = username;
        }

        public String getUsername() {
            return username;
        }

        @Override
        public String getType() {
            return "SYSTEM_REQUEST";
        }
    }

18. 同10 解释下面内容：
// 系统广播消息
    public static class SystemBroadcast extends SystemMessage {
        private static final long serialVersionUID = 1L;
        private String type;
        private String username;

        public SystemBroadcast(MessageContent content, String type, String username) {
            super(content);
            this.type = type;
            this.username = username;
        }

        public String getBroadcastType() {
            return type;
        }

        public String getUsername() {
            return username;
        }

        @Override
        public String getType() {
            return "SYSTEM_BROADCAST";
        }
    }

19. 同10 解释下面内容：
// 系统用户列表消息
    public static class SystemUserList extends SystemMessage {
        private static final long serialVersionUID = 1L;
        private List<String> users;

        public SystemUserList(MessageContent content, List<String> users) {
            super(content);
            this.users = users;
        }

        public List<String> getUsers() {
            return users;
        }

        @Override
        public String getType() {
            return "SYSTEM_USER_LIST";
        }
    }

20. 请你详细解释下面内容的逻辑、组织方式、语法、优化点
// 消息格式化工具类
    public static class Messages {
        // 生成消息的时间戳和发送者前缀
        public static String getMessagePrefix(Message message, String currentUsername) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
            String timestamp = LocalDateTime.now().format(formatter);

            if (message instanceof UserBroadcastMessage) {
                UserBroadcastMessage ubm = (UserBroadcastMessage) message;
                String sender = ubm.isAnonymous() ? "匿名用户" : ubm.getSender();
                return String.format("[%s] %s", timestamp, sender);
            } else if (message instanceof UserPrivateMessage) {
                UserPrivateMessage upm = (UserPrivateMessage) message;
                String sender = upm.isAnonymous() ? "匿名用户" : upm.getSender();
                if (Objects.equals(upm.getSender(), currentUsername)) {
                    return String.format("[%s] 你 -> %s (私聊)", timestamp, upm.getReceiver());
                } else {
                    return String.format("[%s] %s -> 你 (私聊)", timestamp, sender);
                }
            } else if (message instanceof SystemMessage) {
                return String.format("[%s] 系统", timestamp);
            }
            return String.format("[%s] 未知", timestamp);
        }

        public static String getMessageContent(Message message) {
            return message.getContent().getContent().toString();
        }
    }


ChatRoomServer类：
1. 请你解释下面这些导入的内容分别都提供什么服务，以及给出其在代码中的使用内容：
import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;
import javax.swing.text.BadLocationException;
import javax.swing.text.StyledDocument;
import java.awt.Color;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

2. 请你解释下面内容中是如何实现用户账户管理的，以及该部分所使用的语法：
// 用户账户管理类
    public static class UserManager {
        private final Map<String, String> users;  // 用户名->密码映射表

        public UserManager(String userFilePath) {
            users = new HashMap<String, String>();
            loadUsers(userFilePath);
        }

3. 请你说明下面内容的详细语法规则以及其异常处理是如何实现的？
// 从文件中加载用户数据，格式为"用户名:密码"
        private void loadUsers(String userFilePath) {
            try (BufferedReader reader = new BufferedReader(new FileReader(userFilePath))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    String[] parts = line.split(":");
                    if (parts.length == 2) {
                        users.put(parts[0].trim(), parts[1].trim());
                    }
                }
            } catch (IOException e) {
                System.err.println("Error loading user file: " + e.getMessage());
            }
        }


4. 请你解释下面内容的作用：
public boolean authenticate(String username, String password) {
            return users.containsKey(username) && users.get(username).equals(password);
        }

        public List<String> getAllUsers() {
            return new ArrayList<String>(users.keySet());
        }

        public boolean isUserExist(String username) {
            return users.containsKey(username);
        }

5. 请你详细介绍下面内容的实现，以及其涉及的语法：
// 服务器日志记录工具
    public static class Logger {
        private final String logfile;  // 日志文件路径
        private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        public Logger(String logfile) {
            this.logfile = logfile;
        }

        public synchronized void log(String message) {
            String logMessage = String.format("[%s] %s", LocalDateTime.now().format(formatter), message);
            writeToFile(logMessage);
        }

        private void writeToFile(String message) {
            try (FileWriter fw = new FileWriter(logfile, true);
                 BufferedWriter bw = new BufferedWriter(fw);
                 PrintWriter out = new PrintWriter(bw)) {
                out.println(message);
            } catch (IOException e) {
                System.err.println("Error writing to log file: " + e.getMessage());
            }
        }
    }

6. 请你详细介绍 ClientHandler 类的实现以及其内容，要求介绍其所有内容、语法等

7. 请你说明 public static class ServerView extends JFrame 中的JFrame是什么

8. 请你介绍下面内容的具体实现：
// ==================== 服务器视图 ====================
    public static class ServerView extends JFrame {
        private final Server server;
        private JTextPane messagePanel;
        private JPanel serverPanel;
        private JTextField inputField;
        private JButton sendButton;
        private JLabel inputLabel;
        private JScrollPane messageOuterPanel;

        public ServerView(Server server) {
            super("Toronto的聊天室服务器");
            this.server = server;
            initComponents();
            setupEventListeners();
            setupFrame();
        }

        private void initComponents() {
            // 设置现代化的颜色主题
            Color primaryColor = new Color(70, 130, 180);      // 钢蓝色
            Color secondaryColor = new Color(240, 248, 255);   // 爱丽丝蓝
            Color accentColor = new Color(255, 140, 0);        // 深橙色
            Color textColor = new Color(47, 79, 79);           // 深石板灰
            Color logBgColor = new Color(250, 250, 250);       // 日志背景色

            // 创建字体
            Font labelFont = new Font("微软雅黑", Font.BOLD, 14);
            Font fieldFont = new Font("微软雅黑", Font.PLAIN, 13);
            Font buttonFont = new Font("微软雅黑", Font.BOLD, 13);
            Font logFont = new Font("Consolas", Font.PLAIN, 14);

            serverPanel = new JPanel();
            serverPanel.setBackground(secondaryColor);
            serverPanel.setBorder(new EmptyBorder(15, 15, 15, 15));

            // 日志显示区域
            messagePanel = new JTextPane();
            messagePanel.setEditable(false);
            messagePanel.setBackground(logBgColor);
            messagePanel.setBorder(new EmptyBorder(10, 10, 10, 10));
            messagePanel.setFont(logFont);

            // 输入框样式
            inputField = new JTextField();
            inputField.setFont(fieldFont);
            inputField.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(primaryColor, 1),
                BorderFactory.createEmptyBorder(8, 12, 8, 12)));
            inputField.setPreferredSize(new Dimension(300, 35));

            // 按钮样式
            sendButton = createStyledButton("发送", primaryColor, Color.WHITE, buttonFont);

            // 标签样式
            inputLabel = createStyledLabel("服务器命令:", labelFont, textColor);

            // 滚动面板
            messageOuterPanel = new JScrollPane(messagePanel);
            messageOuterPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(primaryColor, 1), "服务器日志",
                TitledBorder.LEFT, TitledBorder.TOP, labelFont, textColor));
            messageOuterPanel.setBackground(logBgColor);

            // 设置布局
            GroupLayout layout = new GroupLayout(serverPanel);
            serverPanel.setLayout(layout);
            layout.setHorizontalGroup(
                    layout.createParallelGroup(GroupLayout.Alignment.LEADING)
                            .addComponent(messageOuterPanel)
                            .addGroup(layout.createSequentialGroup()
                                    .addComponent(inputLabel)
                                    .addComponent(inputField)
                                    .addComponent(sendButton))
            );
            layout.setVerticalGroup(
                    layout.createSequentialGroup()
                            .addComponent(messageOuterPanel, 0, 400, Short.MAX_VALUE)
                            .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
                                    .addComponent(inputLabel)
                                    .addComponent(inputField, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)
                                    .addComponent(sendButton))
            );
        }

        private void setupEventListeners() {
            inputField.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    send();
                }
            });
            sendButton.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    send();
                }
            });
        }

        private void setupFrame() {
            try {
                setIconImage(new ImageIcon(ChatRoomShared.Constants.ICON_FILE).getImage());
            } catch (Exception e) {
                // 图标加载失败时忽略
            }
            setContentPane(serverPanel);
            setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            setSize(800, 600);  // 设置更大的窗口尺寸
            setLocationRelativeTo(null);
            setVisible(true);
        }

        private void send() {
            String content = inputField.getText().trim();
            server.handleServerCommands(content);
            inputField.setText("");
        }

        public void display(String message) {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            StyledDocument doc = messagePanel.getStyledDocument();

            // 创建样式
            javax.swing.text.SimpleAttributeSet timestampStyle = new javax.swing.text.SimpleAttributeSet();
            javax.swing.text.StyleConstants.setForeground(timestampStyle, new Color(128, 128, 128));  // 灰色时间戳
            javax.swing.text.StyleConstants.setFontFamily(timestampStyle, "Consolas");
            javax.swing.text.StyleConstants.setFontSize(timestampStyle, 14);

            javax.swing.text.SimpleAttributeSet messageStyle = new javax.swing.text.SimpleAttributeSet();
            javax.swing.text.StyleConstants.setForeground(messageStyle, new Color(47, 79, 79));  // 深石板灰
            javax.swing.text.StyleConstants.setFontFamily(messageStyle, "微软雅黑");
            javax.swing.text.StyleConstants.setFontSize(messageStyle, 14);

            try {
                // 插入时间戳
                doc.insertString(doc.getLength(), "[" + timestamp + "] ", timestampStyle);
                // 插入消息内容
                doc.insertString(doc.getLength(), message + "\n", messageStyle);
                messagePanel.setCaretPosition(doc.getLength());
            } catch (BadLocationException e) {
                throw new RuntimeException(e);
            }
        }

        // 创建样式化按钮的辅助方法
        private JButton createStyledButton(String text, Color bgColor, Color fgColor, Font font) {
            JButton button = new JButton(text);
            button.setFont(font);
            button.setBackground(bgColor);
            button.setForeground(fgColor);
            button.setBorder(BorderFactory.createEmptyBorder(10, 20, 10, 20));
            button.setFocusPainted(false);
            button.setCursor(new Cursor(Cursor.HAND_CURSOR));
            button.setPreferredSize(new Dimension(100, 40));

            // 添加鼠标悬停效果
            button.addMouseListener(new java.awt.event.MouseAdapter() {
                public void mouseEntered(java.awt.event.MouseEvent evt) {
                    button.setBackground(bgColor.brighter());
                }
                public void mouseExited(java.awt.event.MouseEvent evt) {
                    button.setBackground(bgColor);
                }
            });

            return button;
        }

        // 创建样式化标签的辅助方法
        private JLabel createStyledLabel(String text, Font font, Color color) {
            JLabel label = new JLabel(text);
            label.setFont(font);
            label.setForeground(color);
            return label;
        }
    }


9. 请你详细介绍服务器核心管理类 Server 的实现以及其语法规则及其有哪些可以优化的内容

ChatRoomClient类：

1. 请你逐行解释ChatRoomClient类的内容，要求着重于其组织方式和语法规则

其他问题：
1. 请你分别解释这三个大类都是如何实现的？以及其组织方式？
2. 请你解释其是如何实现服务器与客户端进行通信的
3. 请你给出几个本项目中较为重要的库，如果我想自己实现并封装这些库，我该做些什么？
4. 在本项目中你实现的异常处理都有哪些，请你给出具体代码及其位置
5. 请你给出本项目实现下述功能代码的具体内容及其位置，及其实现思路（功能我全部放在一个（）内了）：
（1、启动时首先读入一个用户文件，用户文件中保存的是全部用户（至少10个）的用户名及密码（明文保存，真实系统不会明文保存的）。然后显示一个提示符，等待输入命令。可接受的命令包括list：列出全部在线用户；listall：列出全部用户；quit：退出系统；
2、在固定端口（8000以上）侦听，等待客户端连接；
3、客户端连接成功后有一个验证用户名和密码的过程。如果用户名密码正确，要给该客户端发成功验证的提示信息，否则给客户端发送用户名或密码错误信息，并等待用户再次验证，直到验证成功或客户端断开；
4、至少支持5个客户端同时聊天；
5、转发客户端发送的聊天信息且支持匿名聊天；
6、日志功能。用文本文件记录每个用户的行为。这些行为包括每次登录成功信息（要包含IP地址，时间等）、每次登录失败信息（要包含IP地址、时间等）、每次退出聊天室时间等。

客户端功能要求：
1、启动后首先要连接到服务器端；
2、连接成功后提示用户输入用户名及密码，并将用户输入的用户名及密码发给服务器端验证。验证成功后则成功进入聊天室，否则提示用户重新输入用户名及密码，直到验证成功或用户结束程序；
3、验证成功后进入聊天室，聊天室要有提示符；
4、聊天室内要能显示所有用户的非私聊信息及给自己的私聊信息；
5、在聊天室内用户可以输入三类字符串：①普通字符串，为广播类聊天信息，所有人都可以看到；②以@+用户名开头的字符串，为私聊信息，只有@到的那个用户可以看到；③@@+命令，为系统命令；
6、系统命令有：list：列出当前在线用户；quit：退出系统；showanonymous：显示当前聊天方式是否为匿名；anonymous：切换聊天方式，即使用匿名聊天，还是实名聊天。）

6. 客户端和服务器是如何通信的？

7. 一条聊天消息从发送到显示经历了什么？

8. 用户是如何登录和验证的？

9. 服务器如何同时处理多个客户端？

10. 用户的操作是如何转换为网络消息的？
















