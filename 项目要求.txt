设计并实现一个基于图形界面的C/S结构的简易聊天室程序

程序分为服务器端和客户端两部分：
服务器端功能要求：
1、启动时首先读入一个用户文件，用户文件中保存的是全部用户（至少10个）的用户名及密码（明文保存，真实系统不会明文保存的）。然后显示一个提示符，等待输入命令。可接受的命令包括list：列出全部在线用户；listall：列出全部用户；quit：退出系统；
2、在固定端口（8000以上）侦听，等待客户端连接；
3、客户端连接成功后有一个验证用户名和密码的过程。如果用户名密码正确，要给该客户端发成功验证的提示信息，否则给客户端发送用户名或密码错误信息，并等待用户再次验证，直到验证成功或客户端断开；
4、至少支持5个客户端同时聊天；
5、转发客户端发送的聊天信息且支持匿名聊天；
6、日志功能。用文本文件记录每个用户的行为。这些行为包括每次登录成功信息（要包含IP地址，时间等）、每次登录失败信息（要包含IP地址、时间等）、每次退出聊天室时间等。

客户端功能要求：
1、启动后首先要连接到服务器端；
2、连接成功后提示用户输入用户名及密码，并将用户输入的用户名及密码发给服务器端验证。验证成功后则成功进入聊天室，否则提示用户重新输入用户名及密码，直到验证成功或用户结束程序；
3、验证成功后进入聊天室，聊天室要有提示符；
4、聊天室内要能显示所有用户的非私聊信息及给自己的私聊信息；
5、在聊天室内用户可以输入三类字符串：①普通字符串，为广播类聊天信息，所有人都可以看到；②以@+用户名开头的字符串，为私聊信息，只有@到的那个用户可以看到；③@@+命令，为系统命令；
6、系统命令有：list：列出当前在线用户；quit：退出系统；showanonymous：显示当前聊天方式是否为匿名；anonymous：切换聊天方式，即使用匿名聊天，还是实名聊天。

验收时间：2024年6月21日。具体时间地点待定。

文档要求：
写明自己的设计思路及每个类的功能。
使用说明，类的关系。

提交要求：
作业提交时间：验收后一周内。

提交文件命名要求：
请大家严格按照下述要求进行命名，否则有可能找不到你的作业从而没有成绩！
1、只提交源代码，不要提交工程； 
2、将所有源代码放到一个名字为学号的目录下；
3、将文档、源代码、用户文件等压缩成一个文件提交；
4、文档、压缩文件及邮件标题命名格式统一为：班级+学号+姓名。