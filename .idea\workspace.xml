<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="ab93fe83-8900-4425-a304-57b650a82cb2" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="ProjectId" id="2yLot3b2PeUKjK9yoH2lGbRCScq" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="project.structure.last.edited" value="Modules" />
    <property name="project.structure.proportion" value="0.15" />
    <property name="project.structure.side.proportion" value="0.2" />
    <property name="settings.editor.selected.configurable" value="preferences.updates" />
  </component>
  <component name="RunManager" selected="Application.ChatRoom Client">
    <configuration name="ChatRoom Client" type="Application" factoryName="Application" singleton="false">
      <option name="MAIN_CLASS_NAME" value="com.chatroom.ChatRoomClient$Client" />
      <module name="Classroom_1" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ChatRoom Server" type="Application" factoryName="Application">
      <option name="MAIN_CLASS_NAME" value="com.chatroom.ChatRoomServer$Server" />
      <module name="Classroom_1" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.ChatRoom Client" />
      <item itemvalue="Application.ChatRoom Server" />
    </list>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ab93fe83-8900-4425-a304-57b650a82cb2" name="Default Changelist" comment="" />
      <created>1749623427071</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749623427071</updated>
      <workItem from="1749623427869" duration="2486000" />
      <workItem from="1749631265507" duration="3106000" />
      <workItem from="1749741013270" duration="1809000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="WindowStateProjectService">
    <state x="121" y="0" key="#Project_Structure" timestamp="1749624264404">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state x="121" y="0" key="#Project_Structure/0.0.1536.864@0.0.1536.864" timestamp="1749624264404" />
    <state x="219" y="62" width="1207" height="802" key="#com.intellij.execution.impl.EditConfigurationsDialog" timestamp="1749631360852">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state x="219" y="62" width="1207" height="802" key="#com.intellij.execution.impl.EditConfigurationsDialog/0.0.1536.864@0.0.1536.864" timestamp="1749631360852" />
    <state x="660" y="274" key="#com.intellij.ide.util.TreeClassChooserDialog" timestamp="1749624868336">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state x="660" y="274" key="#com.intellij.ide.util.TreeClassChooserDialog/0.0.1536.864@0.0.1536.864" timestamp="1749624868336" />
    <state width="1483" height="192" key="GridCell.Tab.0.bottom" timestamp="1749742796109">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1483" height="192" key="GridCell.Tab.0.bottom/0.0.1536.816@0.0.1536.816" timestamp="1749742796109" />
    <state width="1483" height="208" key="GridCell.Tab.0.bottom/0.0.1536.864@0.0.1536.864" timestamp="1749634517289" />
    <state width="1483" height="192" key="GridCell.Tab.0.center" timestamp="1749742796109">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1483" height="192" key="GridCell.Tab.0.center/0.0.1536.816@0.0.1536.816" timestamp="1749742796109" />
    <state width="1483" height="208" key="GridCell.Tab.0.center/0.0.1536.864@0.0.1536.864" timestamp="1749634517289" />
    <state width="1483" height="192" key="GridCell.Tab.0.left" timestamp="1749742796109">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1483" height="192" key="GridCell.Tab.0.left/0.0.1536.816@0.0.1536.816" timestamp="1749742796109" />
    <state width="1483" height="208" key="GridCell.Tab.0.left/0.0.1536.864@0.0.1536.864" timestamp="1749634517289" />
    <state width="1483" height="192" key="GridCell.Tab.0.right" timestamp="1749742796109">
      <screen x="0" y="0" width="1536" height="816" />
    </state>
    <state width="1483" height="192" key="GridCell.Tab.0.right/0.0.1536.816@0.0.1536.816" timestamp="1749742796109" />
    <state width="1483" height="208" key="GridCell.Tab.0.right/0.0.1536.864@0.0.1536.864" timestamp="1749634517289" />
    <state x="121" y="0" key="SettingsEditor" timestamp="1749623711830">
      <screen x="0" y="0" width="1536" height="864" />
    </state>
    <state x="121" y="0" key="SettingsEditor/0.0.1536.864@0.0.1536.864" timestamp="1749623711830" />
  </component>
</project>