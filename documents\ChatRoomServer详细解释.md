# ChatRoomServer.java 详细解释

## 文件概述
`ChatRoomServer.java` 是聊天室项目的服务器端主文件，包含了用户管理、日志记录、客户端处理、服务器界面和服务器核心逻辑等功能。

## 包声明和导入语句

```java
package com.chatroom;
```
- **第1行**: 声明包名为 `com.chatroom`

```java
import javax.swing.*;
import javax.swing.text.BadLocationException;
import javax.swing.text.StyledDocument;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
```
- **第3-7行**: 导入Swing GUI组件和事件处理类
  - `J<PERSON>rame`, `JButton`, `JTextField`等GUI组件
  - `StyledDocument`用于富文本显示
  - `ActionEvent`, `ActionListener`用于事件处理

```java
import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
```
- **第8-11行**: 导入网络和IO相关类
  - `ServerSocket`, `Socket`用于网络连接
  - `SocketException`用于处理网络异常
  - IO类用于文件和流操作

```java
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
```
- **第12-17行**: 导入时间处理、集合和并发相关类
  - `LocalDateTime`, `DateTimeFormatter`用于时间格式化
  - 各种集合类
  - `ExecutorService`用于线程池管理
  - `CopyOnWriteArrayList`用于线程安全的列表操作

## 主类声明

```java
/**
 * 聊天室服务器端 - 包含所有服务器相关功能
 * Java 8 兼容版本
 */
public class ChatRoomServer {
```
- **第19-23行**: 类的JavaDoc注释和类声明

## UserManager 用户管理器类

### 类声明和成员变量

```java
public static class UserManager {
    private final Map<String, String> users;
```
- **第26-27行**: UserManager类声明和成员变量
  - `users`: 存储用户名和密码的映射表

### 构造函数

```java
public UserManager(String userFilePath) {
    users = new HashMap<String, String>();
    loadUsers(userFilePath);
}
```
- **第29-32行**: 构造函数
  - 初始化用户映射表
  - 调用loadUsers方法从文件加载用户数据

### 用户数据加载方法

```java
private void loadUsers(String userFilePath) {
    try (BufferedReader reader = new BufferedReader(new FileReader(userFilePath))) {
        String line;
        while ((line = reader.readLine()) != null) {
            String[] parts = line.split(":");
            if (parts.length == 2) {
                users.put(parts[0].trim(), parts[1].trim());
            }
        }
    } catch (IOException e) {
        System.err.println("Error loading user file: " + e.getMessage());
    }
}
```
- **第34-46行**: 从文件加载用户数据
  - 使用try-with-resources自动关闭文件流
  - 逐行读取用户文件
  - 按冒号分割每行，获取用户名和密码
  - 去除首尾空格后存入映射表
  - 捕获并处理IO异常

### 用户验证和查询方法

```java
public boolean authenticate(String username, String password) {
    return users.containsKey(username) && users.get(username).equals(password);
}
```
- **第48-50行**: 用户身份验证方法
  - 检查用户名是否存在且密码匹配

```java
public List<String> getAllUsers() {
    return new ArrayList<String>(users.keySet());
}
```
- **第52-54行**: 获取所有用户列表
  - 返回所有用户名的列表副本

```java
public boolean isUserExist(String username) {
    return users.containsKey(username);
}
```
- **第56-58行**: 检查用户是否存在
  - 检查用户名是否在映射表中

## Logger 日志记录器类

### 类声明和成员变量

```java
public static class Logger {
    private final String logfile;
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
```
- **第62-64行**: Logger类声明和成员变量
  - `logfile`: 日志文件路径
  - `formatter`: 时间格式化器，格式为"年-月-日 时:分:秒"

### 构造函数

```java
public Logger(String logfile) {
    this.logfile = logfile;
}
```
- **第66-68行**: 构造函数，设置日志文件路径

### 日志记录方法

```java
public synchronized void log(String message) {
    String logMessage = String.format("[%s] %s", LocalDateTime.now().format(formatter), message);
    writeToFile(logMessage);
}
```
- **第70-73行**: 记录日志方法
  - 使用synchronized确保线程安全
  - 格式化日志消息，添加时间戳
  - 调用writeToFile写入文件

```java
private void writeToFile(String message) {
    try (FileWriter fw = new FileWriter(logfile, true);
         BufferedWriter bw = new BufferedWriter(fw);
         PrintWriter out = new PrintWriter(bw)) {
        out.println(message);
    } catch (IOException e) {
        System.err.println("Error writing to log file: " + e.getMessage());
    }
}
```
- **第75-83行**: 写入文件方法
  - 使用try-with-resources自动关闭文件流
  - 以追加模式打开文件
  - 使用BufferedWriter和PrintWriter提高写入效率
  - 捕获并处理IO异常

## ClientHandler 客户端处理器类

### 类声明和成员变量

```java
public static class ClientHandler implements Runnable {
    private final Socket socket;
    private final Server server;
    private ObjectInputStream in;
    private ObjectOutputStream out;
    private String username;
    private boolean authenticated = false;
```
- **第87-93行**: ClientHandler类声明和成员变量
  - 实现Runnable接口，可在独立线程中运行
  - `socket`: 客户端连接套接字
  - `server`: 服务器实例引用
  - `in`, `out`: 对象输入输出流
  - `username`: 客户端用户名
  - `authenticated`: 是否已通过身份验证

### 构造函数

```java
public ClientHandler(Socket socket, Server server) {
    this.socket = socket;
    this.server = server;
}
```
- **第95-98行**: 构造函数，初始化套接字和服务器引用

### 主运行方法

```java
@Override
public void run() {
    try {
        in = new ObjectInputStream(socket.getInputStream());
        out = new ObjectOutputStream(socket.getOutputStream());
        if (authenticate())
            loop();
    } catch (IOException e) {
        server.output("Error handling client: " + e.getMessage());
    } catch (ClassNotFoundException e) {
        server.output("Error handling client: " + e.getMessage());
    } finally {
        close(false);
    }
}
```
- **第100-114行**: 线程主运行方法
  - 创建对象输入输出流
  - 进行身份验证
  - 验证成功后进入消息处理循环
  - 捕获异常并记录错误
  - 最终关闭连接

### 消息处理循环

```java
private void loop() {
    try {
        ChatRoomShared.Message inputMessage;
        while ((inputMessage = (ChatRoomShared.Message) in.readObject()) != null) {
            handleClientMessage(inputMessage);
            if (socket.isClosed())
                close(false);
        }
    } catch (SocketException ignored) {
    } catch (IOException e) {
        server.output("Error handling client: " + e.getMessage());
    } catch (ClassNotFoundException e) {
        server.output("Error handling client: " + e.getMessage());
    }
}
```
- **第116-130行**: 消息处理循环
  - 持续读取客户端发送的消息对象
  - 调用handleClientMessage处理每条消息
  - 检查套接字是否已关闭
  - 忽略SocketException（正常断开连接）
  - 记录其他异常

### 日志记录方法

```java
public void logLogin(String username, String ip, boolean success) {
    String status = success ? "successful" : "failed";
    server.output(String.format("Login %s for user %s from IP %s", status, username, ip));
}

public void logLogout(String username) {
    server.output(String.format("User %s logged out", username));
}
```
- **第132-139行**: 登录和登出日志记录方法
  - 记录登录成功或失败的信息，包含用户名和IP地址
  - 记录用户登出信息

### 身份验证方法

```java
private boolean authenticate() throws IOException, ClassNotFoundException {
    while (!authenticated) {
        ChatRoomShared.SystemRequest usernameRequest = (ChatRoomShared.SystemRequest) in.readObject();
        String username = (String) usernameRequest.getContent().getContent();

        ChatRoomShared.SystemRequest passwordRequest = (ChatRoomShared.SystemRequest) in.readObject();
        String password = (String) passwordRequest.getContent().getContent();

        if (server.getUserManager().isUserExist(username)) {
            if (server.getUserManager().authenticate(username, password)) {
                if (!server.isUserAlreadyLogin(username)) {
                    authenticated = true;
                    this.username = username;
                    out.writeObject(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent(ChatRoomShared.SystemReply.LOGIN_SUCCESS)));
                    out.writeObject(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent("Authentication successful. Welcome to the chat room!")));
                    server.broadcastMessage(new ChatRoomShared.SystemBroadcast(new ChatRoomShared.TextMessageContent(username + " has joined the chat."), "join", username));
                    logLogin(username, ChatRoomShared.NetworkUtil.getIpAddress(socket), authenticated);
                    return true;
                } else
                    out.writeObject(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent(ChatRoomShared.SystemReply.ALREADY_LOGIN)));
            } else
                out.writeObject(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent(ChatRoomShared.SystemReply.PASSWORD_INCORRECT)));
        } else
            out.writeObject(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent(ChatRoomShared.SystemReply.USER_NOT_EXIST)));

        logLogin(username, ChatRoomShared.NetworkUtil.getIpAddress(socket), authenticated);
    }
    return false;
}
```
- **第141-169行**: 身份验证方法
  - 循环等待客户端发送用户名和密码
  - 读取用户名请求和密码请求
  - 检查用户是否存在
  - 验证密码是否正确
  - 检查用户是否已经登录
  - 验证成功：
    - 设置authenticated为true
    - 发送登录成功消息
    - 发送欢迎消息
    - 广播用户加入消息
    - 记录登录日志
  - 验证失败：发送相应的错误消息
  - 记录登录尝试日志

### 客户端消息处理方法

```java
private void handleClientMessage(ChatRoomShared.Message message) {
    if (message instanceof ChatRoomShared.UserBroadcastMessage) {
        ChatRoomShared.UserBroadcastMessage ubm = (ChatRoomShared.UserBroadcastMessage) message;
        server.broadcastMessage(ubm);
    } else if (message instanceof ChatRoomShared.UserPrivateMessage) {
        ChatRoomShared.UserPrivateMessage upm = (ChatRoomShared.UserPrivateMessage) message;
        boolean userExist = server.isUserAlreadyLogin(upm.getReceiver());
        if (!userExist)
            sendMessage(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent("User " + upm.getReceiver() + " is not online or existed. Please try again.")));
        else {
            server.sendPrivateMessage(upm);
            sendMessage(upm);
        }
    } else if (message instanceof ChatRoomShared.SystemRequest) {
        ChatRoomShared.SystemRequest sr = (ChatRoomShared.SystemRequest) message;
        if (!Objects.equals(username, sr.getUsername()))
            throw new IllegalStateException("Unexpected value: " + sr);
        handleCommand((String) sr.getContent().getContent());
    } else {
        throw new IllegalStateException("Unexpected value: " + message);
    }
}
```
- **第171-192行**: 处理客户端消息
  - **UserBroadcastMessage**: 群聊消息，直接广播给所有用户
  - **UserPrivateMessage**: 私聊消息
    - 检查接收者是否在线
    - 如果不在线，发送错误消息
    - 如果在线，发送私聊消息给接收者，并回显给发送者
  - **SystemRequest**: 系统请求
    - 验证请求的用户名与当前用户匹配
    - 调用handleCommand处理命令
  - 其他类型消息抛出异常

### 命令处理方法

```java
private void handleCommand(String command) {
    switch (command.toLowerCase()) {
        case "list":
            sendMessage(new ChatRoomShared.SystemUserList(new ChatRoomShared.TextMessageContent(""), server.getOnlineUsers()));
            sendMessage(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent("Online users: " + server.getOnlineUsers())));
            break;
        case "quit":
            close(false);
            break;
        default:
            sendMessage(new ChatRoomShared.SystemReply(new ChatRoomShared.TextMessageContent("Unknown command. Available commands: list, quit")));
    }
}
```
- **第194-206行**: 处理客户端命令
  - **list**: 发送在线用户列表和文本提示
  - **quit**: 关闭客户端连接
  - **其他命令**: 发送未知命令提示

### 消息发送和连接关闭方法

```java
public void sendMessage(ChatRoomShared.Message message) {
    try {
        out.writeObject(message);
    } catch (IOException e) {
        server.output("Error sending message: " + e.getMessage());
    }
}
```
- **第208-214行**: 发送消息给客户端
  - 通过输出流发送消息对象
  - 捕获并记录IO异常

```java
public void close(boolean shutdown) {
    try {
        if (username != null) {
            server.removeClient(this);
            if (!shutdown)
                server.broadcastMessage(new ChatRoomShared.SystemBroadcast(new ChatRoomShared.TextMessageContent(username + " has left the chat."), "left", username));
            logLogout(username);
            username = null;
            authenticated = false;
        }
        socket.close();
    } catch (IOException e) {
        server.output("Error closing client connection: " + e.getMessage());
    }
}
```
- **第216-230行**: 关闭客户端连接
  - 如果用户已登录：
    - 从服务器移除客户端
    - 如果不是服务器关闭，广播用户离开消息
    - 记录登出日志
    - 清空用户名和认证状态
  - 关闭套接字连接
  - 捕获并记录IO异常

### 获取器方法

```java
public String getUsername() {
    return username;
}

public boolean isAuthenticated() {
    return authenticated;
}
```
- **第232-239行**: 获取用户名和认证状态的方法

## ServerView 服务器视图类

### 类声明和成员变量

```java
public static class ServerView extends JFrame {
    private final Server server;
    private JTextPane messagePanel;
    private JPanel serverPanel;
    private JTextField inputField;
    private JButton sendButton;
    private JLabel inputLabel;
    private JScrollPane messageOuterPanel;
```
- **第242-249行**: ServerView类声明和成员变量
  - 继承自JFrame，是服务器的GUI界面
  - `server`: 服务器实例引用
  - 定义了服务器界面所需的GUI组件

### 构造函数

```java
public ServerView(Server server) {
    super("简易聊天室-服务器");
    this.server = server;
    initComponents();
    setupEventListeners();
    setupFrame();
}
```
- **第251-257行**: 构造函数
  - 设置窗口标题为"简易聊天室-服务器"
  - 保存服务器实例引用
  - 依次调用初始化方法

### 组件初始化方法

```java
private void initComponents() {
    serverPanel = new JPanel();
    messagePanel = new JTextPane();
    inputField = new JTextField();
    sendButton = new JButton("发送");
    inputLabel = new JLabel("命令输入:");
    messageOuterPanel = new JScrollPane(messagePanel);
```
- **第259-265行**: 初始化GUI组件
  - 创建主面板、消息显示面板、输入框、按钮等
  - 为消息面板添加滚动条

### 服务器界面布局设置

```java
// 设置布局
GroupLayout layout = new GroupLayout(serverPanel);
serverPanel.setLayout(layout);
layout.setHorizontalGroup(
    layout.createParallelGroup(GroupLayout.Alignment.LEADING)
        .addComponent(messageOuterPanel)
        .addGroup(layout.createSequentialGroup()
            .addComponent(inputLabel)
            .addComponent(inputField)
            .addComponent(sendButton))
);
layout.setVerticalGroup(
    layout.createSequentialGroup()
        .addComponent(messageOuterPanel, 0, 400, Short.MAX_VALUE)
        .addGroup(layout.createParallelGroup(GroupLayout.Alignment.BASELINE)
            .addComponent(inputLabel)
            .addComponent(inputField, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)
            .addComponent(sendButton))
);
```
- **第267-285行**: 设置服务器界面布局
  - 上部：消息显示区域（可变高度，最小400像素）
  - 下部：命令输入区域（标签+输入框+发送按钮）
  - 使用GroupLayout进行布局管理

### 事件监听器设置

```java
private void setupEventListeners() {
    inputField.addActionListener(new ActionListener() {
        @Override
        public void actionPerformed(ActionEvent e) {
            send();
        }
    });
    sendButton.addActionListener(new ActionListener() {
        @Override
        public void actionPerformed(ActionEvent e) {
            send();
        }
    });
}
```
- **第287-300行**: 设置事件监听器
  - 输入框回车和发送按钮点击都触发send()方法

### 窗口设置方法

```java
private void setupFrame() {
    try {
        setIconImage(new ImageIcon(ChatRoomShared.Constants.ICON_FILE).getImage());
    } catch (Exception e) {
        // 图标加载失败时忽略
    }
    setContentPane(serverPanel);
    setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
    pack();
    setLocationRelativeTo(null);
    setVisible(true);
}
```
- **第302-313行**: 设置窗口属性
  - 设置窗口图标
  - 设置内容面板、关闭操作、自动调整大小、居中显示

### 命令发送和消息显示方法

```java
private void send() {
    String content = inputField.getText().trim();
    server.handleServerCommands(content);
    inputField.setText("");
}
```
- **第315-319行**: 发送服务器命令
  - 获取输入框内容并去除首尾空格
  - 调用服务器的命令处理方法
  - 清空输入框

```java
public void display(String message) {
    String logMessage = String.format("[%s] %s\n",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), message);
    StyledDocument doc = messagePanel.getStyledDocument();
    try {
        doc.insertString(doc.getLength(), logMessage, null);
        messagePanel.setCaretPosition(doc.getLength());
    } catch (BadLocationException e) {
        throw new RuntimeException(e);
    }
}
```
- **第321-332行**: 显示消息到服务器界面
  - 格式化消息，添加时间戳
  - 获取文档对象
  - 在文档末尾插入消息
  - 移动光标到文档末尾

## Server 服务器主类

### 类声明和成员变量

```java
public static class Server {
    private final List<ClientHandler> clients;
    private final UserManager userManager;
    private final Logger logger;
    private final ExecutorService pool;
    private ServerSocket serverSocket;
    private boolean running;
    private ServerView serverView;
```
- **第336-343行**: Server类声明和成员变量
  - `clients`: 线程安全的客户端处理器列表
  - `userManager`: 用户管理器实例
  - `logger`: 日志记录器实例
  - `pool`: 线程池，用于处理客户端连接
  - `serverSocket`: 服务器套接字
  - `running`: 服务器运行状态标志
  - `serverView`: 服务器界面实例

### 构造函数

```java
public Server() {
    this.userManager = new UserManager(ChatRoomShared.Constants.USER_FILE);
    this.logger = new Logger(ChatRoomShared.Constants.LOG_FILE);
    this.clients = new CopyOnWriteArrayList<ClientHandler>();
    this.pool = Executors.newCachedThreadPool();
    this.running = false;
}
```
- **第345-351行**: 构造函数
  - 创建用户管理器，加载用户文件
  - 创建日志记录器
  - 使用CopyOnWriteArrayList确保线程安全
  - 创建缓存线程池
  - 初始化运行状态为false

### 主方法

```java
public static void main(String[] args) {
    new Server().start();
}
```
- **第353-355行**: 程序入口点
  - 创建服务器实例并启动

### 输出和日志方法

```java
public void output(String message) {
    if (serverView != null) {
        serverView.display(message);
    }
    logger.log(message);
}
```
- **第357-362行**: 输出消息方法
  - 如果服务器界面存在，显示到界面
  - 记录到日志文件

### 客户端连接处理方法

```java
private void waitForClient() {
    try {
        serverSocket = new ServerSocket(ChatRoomShared.Constants.PORT);
        running = true;
        output("Server started on port " + ChatRoomShared.Constants.PORT);

        while (running) {
            Socket clientSocket = serverSocket.accept();
            ClientHandler clientHandler = new ClientHandler(clientSocket, this);
            clients.add(clientHandler);
            pool.execute(clientHandler);
        }
    } catch (SocketException ignored) {
    } catch (Exception e) {
        output("Error starting server: " + e.getMessage());
    } finally {
        stop();
    }
}
```
- **第364-382行**: 等待客户端连接方法
  - 创建服务器套接字，监听指定端口
  - 设置运行状态为true
  - 输出服务器启动信息
  - 循环等待客户端连接
  - 为每个连接创建ClientHandler
  - 添加到客户端列表并提交给线程池执行
  - 忽略SocketException（正常关闭）
  - 记录其他异常
  - 最终调用stop()方法

### 服务器启动和停止方法

```java
public void start() {
    serverView = new ServerView(this);
    new Thread(new Runnable() {
        @Override
        public void run() {
            waitForClient();
        }
    }).start();
}
```
- **第384-392行**: 启动服务器方法
  - 创建服务器界面
  - 在新线程中运行waitForClient()方法

```java
public void stop() {
    running = false;
    try {
        if (serverSocket != null && !serverSocket.isClosed())
            serverSocket.close();
        for (ClientHandler clientHandler : clients)
            clientHandler.close(true);
        clients.clear();
        pool.shutdown();
    } catch (IOException e) {
        output("Error stopping server: " + e.getMessage());
    }
    System.exit(0);
}
```
- **第394-407行**: 停止服务器方法
  - 设置运行状态为false
  - 关闭服务器套接字
  - 关闭所有客户端连接
  - 清空客户端列表
  - 关闭线程池
  - 退出程序

### 消息广播和私聊方法

```java
public void broadcastMessage(ChatRoomShared.Message message) {
    for (ClientHandler client : clients) {
        if (client != null && client.isAuthenticated()) {
            client.sendMessage(message);
        }
    }
}
```
- **第409-415行**: 广播消息方法
  - 遍历所有客户端
  - 只向已认证的客户端发送消息

```java
public void sendPrivateMessage(ChatRoomShared.UserPrivateMessage message) {
    for (ClientHandler client : clients) {
        if (client != null && client.isAuthenticated() &&
                Objects.equals(client.getUsername(), message.getReceiver())) {
            client.sendMessage(message);
        }
    }
}
```
- **第417-424行**: 发送私聊消息方法
  - 遍历所有客户端
  - 找到接收者用户名匹配的已认证客户端
  - 发送私聊消息

### 服务器命令处理方法

```java
public void handleServerCommands(String command) {
    switch (command.toLowerCase()) {
        case "list":
            output("Online users: " + getOnlineUsers());
            break;
        case "listall":
            output("All users: " + userManager.getAllUsers());
            break;
        case "quit":
            output("quit");
            stop();
            break;
        default:
            output("Unknown command. Available commands: list, listall, quit");
    }
}
```
- **第426-441行**: 处理服务器命令
  - **list**: 显示在线用户列表
  - **listall**: 显示所有注册用户列表
  - **quit**: 输出退出信息并停止服务器
  - **其他命令**: 显示帮助信息

### 工具方法

```java
public List<String> getOnlineUsers() {
    List<String> onlineUsers = new ArrayList<String>();
    for (ClientHandler client : clients) {
        if (client != null && client.isAuthenticated()) {
            onlineUsers.add(client.getUsername());
        }
    }
    return onlineUsers;
}
```
- **第443-451行**: 获取在线用户列表
  - 遍历所有客户端
  - 收集已认证客户端的用户名

```java
public boolean isUserAlreadyLogin(String username) {
    return getOnlineUsers().contains(username);
}
```
- **第453-455行**: 检查用户是否已登录
  - 检查用户名是否在在线用户列表中

```java
public UserManager getUserManager() {
    return userManager;
}

public void removeClient(ClientHandler clientHandler) {
    clients.remove(clientHandler);
}
```
- **第457-463行**: 获取器和客户端移除方法
  - 获取用户管理器实例
  - 从客户端列表中移除指定客户端

## 总结

ChatRoomServer.java文件实现了完整的聊天室服务器功能：

1. **UserManager**: 管理用户账户，从文件加载用户数据，提供身份验证功能
2. **Logger**: 记录服务器运行日志，支持时间戳格式化
3. **ClientHandler**: 处理单个客户端连接，包括身份验证、消息处理、命令执行
4. **ServerView**: 提供服务器管理界面，显示日志和接受管理命令
5. **Server**: 服务器核心类，管理客户端连接、消息广播、服务器生命周期

整个设计采用多线程架构，使用线程池处理客户端连接，支持并发访问，通过Socket进行网络通信，实现了完整的聊天室服务器功能。
