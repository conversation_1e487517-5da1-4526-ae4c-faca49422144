package com.chatroom;

import java.io.Serializable;
import java.net.Socket;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 聊天室共享组件 - 包含消息类、常量、工具类等
 * Java 8 兼容版本
 */
public class ChatRoomShared {

    // ==================== 常量定义 ====================
    public static class Constants {
        public static final int PORT = 8888;
        public static final String USER_FILE = "resources/users.txt";
        public static final String LOG_FILE = "logs/chat.log";
        public static final String ICON_FILE = "resources/icon.png";
    }

    // ==================== 网络工具类 ====================
    public static class NetworkUtil {
        public static String getIpAddress(Socket socket) {
            if (socket != null && socket.getInetAddress() != null) {
                return socket.getInetAddress().getHostAddress();
            }
            return "Unknown";
        }
    }

    // ==================== 消息类体系 ====================

    // 消息基类
    public static abstract class Message implements Serializable {
        private static final long serialVersionUID = 1L;
        protected MessageContent content;

        public Message(MessageContent content) {
            this.content = content;
        }

        public MessageContent getContent() {
            return content;
        }

        public abstract String getType();
    }

    // 消息内容接口
    public static interface MessageContent extends Serializable {
        Object getContent();
    }

    // 文本消息内容
    public static class TextMessageContent implements MessageContent {
        private static final long serialVersionUID = 1L;
        private final String text;

        public TextMessageContent(String text) {
            this.text = text;
        }

        @Override
        public Object getContent() {
            return text;
        }

        @Override
        public String toString() {
            return text;
        }
    }

    // 用户消息基类
    public static abstract class UserMessage extends Message {
        private static final long serialVersionUID = 1L;
        protected String sender;
        protected boolean isAnonymous;

        public UserMessage(String sender, boolean isAnonymous, MessageContent content) {
            super(content);
            this.sender = sender;
            this.isAnonymous = isAnonymous;
        }

        public String getSender() {
            return sender;
        }

        public boolean isAnonymous() {
            return isAnonymous;
        }
    }

    // 系统消息基类
    public static abstract class SystemMessage extends Message {
        private static final long serialVersionUID = 1L;

        public SystemMessage(MessageContent content) {
            super(content);
        }
    }

    // 用户广播消息
    public static class UserBroadcastMessage extends UserMessage {
        private static final long serialVersionUID = 1L;

        public UserBroadcastMessage(String sender, boolean isAnonymous, MessageContent content) {
            super(sender, isAnonymous, content);
        }

        @Override
        public String getType() {
            return "USER_BROADCAST";
        }
    }

    // 用户私聊消息
    public static class UserPrivateMessage extends UserMessage {
        private static final long serialVersionUID = 1L;
        private String receiver;

        public UserPrivateMessage(String sender, boolean isAnonymous, String receiver, MessageContent content) {
            super(sender, isAnonymous, content);
            this.receiver = receiver;
        }

        public String getReceiver() {
            return receiver;
        }

        @Override
        public String getType() {
            return "USER_PRIVATE";
        }
    }

    // 系统回复消息
    public static class SystemReply extends SystemMessage {
        private static final long serialVersionUID = 1L;

        public static final String LOGIN_SUCCESS = "LOGIN_SUCCESS";
        public static final String PASSWORD_INCORRECT = "Password incorrect. Please try again.";
        public static final String USER_NOT_EXIST = "User does not exist. Please try again.";
        public static final String ALREADY_LOGIN = "User already logged in. Please try again.";

        public SystemReply(MessageContent content) {
            super(content);
        }

        @Override
        public String getType() {
            return "SYSTEM_REPLY";
        }
    }

    // 系统请求消息
    public static class SystemRequest extends SystemMessage {
        private static final long serialVersionUID = 1L;
        private String username;

        public SystemRequest(String username, MessageContent content) {
            super(content);
            this.username = username;
        }

        public String getUsername() {
            return username;
        }

        @Override
        public String getType() {
            return "SYSTEM_REQUEST";
        }
    }

    // 系统广播消息
    public static class SystemBroadcast extends SystemMessage {
        private static final long serialVersionUID = 1L;
        private String type;
        private String username;

        public SystemBroadcast(MessageContent content, String type, String username) {
            super(content);
            this.type = type;
            this.username = username;
        }

        public String getBroadcastType() {
            return type;
        }

        public String getUsername() {
            return username;
        }

        @Override
        public String getType() {
            return "SYSTEM_BROADCAST";
        }
    }

    // 系统用户列表消息
    public static class SystemUserList extends SystemMessage {
        private static final long serialVersionUID = 1L;
        private List<String> users;

        public SystemUserList(MessageContent content, List<String> users) {
            super(content);
            this.users = users;
        }

        public List<String> getUsers() {
            return users;
        }

        @Override
        public String getType() {
            return "SYSTEM_USER_LIST";
        }
    }

    // ==================== 消息工具类 ====================
    public static class Messages {
        public static String getMessagePrefix(Message message, String currentUsername) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
            String timestamp = LocalDateTime.now().format(formatter);

            if (message instanceof UserBroadcastMessage) {
                UserBroadcastMessage ubm = (UserBroadcastMessage) message;
                String sender = ubm.isAnonymous() ? "匿名用户" : ubm.getSender();
                return String.format("[%s] %s", timestamp, sender);
            } else if (message instanceof UserPrivateMessage) {
                UserPrivateMessage upm = (UserPrivateMessage) message;
                String sender = upm.isAnonymous() ? "匿名用户" : upm.getSender();
                if (Objects.equals(upm.getSender(), currentUsername)) {
                    return String.format("[%s] 你 -> %s (私聊)", timestamp, upm.getReceiver());
                } else {
                    return String.format("[%s] %s -> 你 (私聊)", timestamp, sender);
                }
            } else if (message instanceof SystemMessage) {
                return String.format("[%s] 系统", timestamp);
            }
            return String.format("[%s] 未知", timestamp);
        }

        public static String getMessageContent(Message message) {
            return message.getContent().getContent().toString();
        }
    }
}
