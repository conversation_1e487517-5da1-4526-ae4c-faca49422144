# ChatRoomShared.java 详细解释

## 文件概述
`ChatRoomShared.java` 是聊天室项目的共享组件文件，包含了客户端和服务器端都需要使用的消息类、常量定义、工具类等。这个文件定义了整个聊天室系统的通信协议和数据结构。

## 包声明和导入语句

```java
package com.chatroom;
```
- **第1行**: 声明包名为 `com.chatroom`

```java
import java.io.Serializable;
import java.net.Socket;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
```
- **第3-8行**: 导入必要的类
  - `Serializable`: 用于对象序列化，支持网络传输
  - `Socket`: 用于网络工具方法
  - `LocalDateTime`, `DateTimeFormatter`: 用于时间处理
  - `List`, `Objects`: 用于集合和对象操作

## 主类声明

```java
/**
 * 聊天室共享组件 - 包含消息类、常量、工具类等
 * Java 8 兼容版本
 */
public class ChatRoomShared {
```
- **第10-14行**: 类的JavaDoc注释和类声明
  - 说明这是共享组件类
  - 兼容Java 8版本

## Constants 常量定义类

```java
public static class Constants {
    public static final int PORT = 8888;
    public static final String USER_FILE = "resources/users.txt";
    public static final String LOG_FILE = "logs/chat.log";
    public static final String ICON_FILE = "resources/icon.png";
}
```
- **第17-22行**: 常量定义类
  - `PORT`: 服务器监听端口号，设置为8888
  - `USER_FILE`: 用户数据文件路径
  - `LOG_FILE`: 日志文件路径
  - `ICON_FILE`: 应用程序图标文件路径
  - 所有常量都是public static final，可以被项目中的其他类直接访问

## NetworkUtil 网络工具类

```java
public static class NetworkUtil {
    public static String getIpAddress(Socket socket) {
        if (socket != null && socket.getInetAddress() != null) {
            return socket.getInetAddress().getHostAddress();
        }
        return "Unknown";
    }
}
```
- **第25-32行**: 网络工具类
  - `getIpAddress()`: 从Socket对象获取客户端IP地址
  - 检查socket和地址是否为null，避免空指针异常
  - 如果无法获取地址，返回"Unknown"

## 消息类体系

### Message 消息基类

```java
public static abstract class Message implements Serializable {
    private static final long serialVersionUID = 1L;
    protected MessageContent content;

    public Message(MessageContent content) {
        this.content = content;
    }

    public MessageContent getContent() {
        return content;
    }

    public abstract String getType();
}
```
- **第37-50行**: 消息基类定义
  - 实现`Serializable`接口，支持对象序列化
  - `serialVersionUID`: 序列化版本号，确保兼容性
  - `content`: 消息内容，使用MessageContent接口
  - 构造函数接受MessageContent参数
  - `getContent()`: 获取消息内容
  - `getType()`: 抽象方法，子类必须实现，返回消息类型

### MessageContent 消息内容接口

```java
public static interface MessageContent extends Serializable {
    Object getContent();
}
```
- **第53-55行**: 消息内容接口
  - 继承Serializable接口
  - `getContent()`: 返回实际的内容对象

### TextMessageContent 文本消息内容

```java
public static class TextMessageContent implements MessageContent {
    private static final long serialVersionUID = 1L;
    private final String text;

    public TextMessageContent(String text) {
        this.text = text;
    }

    @Override
    public Object getContent() {
        return text;
    }

    @Override
    public String toString() {
        return text;
    }
}
```
- **第58-75行**: 文本消息内容实现
  - 实现MessageContent接口
  - `text`: 存储文本内容，使用final确保不可变
  - 构造函数接受文本字符串
  - `getContent()`: 返回文本内容
  - `toString()`: 重写toString方法，直接返回文本内容

### UserMessage 用户消息基类

```java
public static abstract class UserMessage extends Message {
    private static final long serialVersionUID = 1L;
    protected String sender;
    protected boolean isAnonymous;

    public UserMessage(String sender, boolean isAnonymous, MessageContent content) {
        super(content);
        this.sender = sender;
        this.isAnonymous = isAnonymous;
    }

    public String getSender() {
        return sender;
    }

    public boolean isAnonymous() {
        return isAnonymous;
    }
}
```
- **第78-96行**: 用户消息基类
  - 继承Message基类
  - `sender`: 发送者用户名
  - `isAnonymous`: 是否匿名发送
  - 构造函数接受发送者、匿名标志和消息内容
  - 调用父类构造函数传递消息内容
  - 提供获取发送者和匿名状态的方法

### SystemMessage 系统消息基类

```java
public static abstract class SystemMessage extends Message {
    private static final long serialVersionUID = 1L;

    public SystemMessage(MessageContent content) {
        super(content);
    }
}
```
- **第99-105行**: 系统消息基类
  - 继承Message基类
  - 用于服务器发送的系统消息
  - 构造函数只需要消息内容

### UserBroadcastMessage 用户广播消息

```java
public static class UserBroadcastMessage extends UserMessage {
    private static final long serialVersionUID = 1L;

    public UserBroadcastMessage(String sender, boolean isAnonymous, MessageContent content) {
        super(sender, isAnonymous, content);
    }

    @Override
    public String getType() {
        return "USER_BROADCAST";
    }
}
```
- **第108-119行**: 用户广播消息类
  - 继承UserMessage基类
  - 用于群聊消息
  - 构造函数调用父类构造函数
  - `getType()`: 返回"USER_BROADCAST"类型标识

### UserPrivateMessage 用户私聊消息

```java
public static class UserPrivateMessage extends UserMessage {
    private static final long serialVersionUID = 1L;
    private String receiver;

    public UserPrivateMessage(String sender, boolean isAnonymous, String receiver, MessageContent content) {
        super(sender, isAnonymous, content);
        this.receiver = receiver;
    }

    public String getReceiver() {
        return receiver;
    }

    @Override
    public String getType() {
        return "USER_PRIVATE";
    }
}
```
- **第122-139行**: 用户私聊消息类
  - 继承UserMessage基类
  - `receiver`: 接收者用户名
  - 构造函数额外接受接收者参数
  - `getReceiver()`: 获取接收者用户名
  - `getType()`: 返回"USER_PRIVATE"类型标识

### SystemReply 系统回复消息

```java
public static class SystemReply extends SystemMessage {
    private static final long serialVersionUID = 1L;

    public static final String LOGIN_SUCCESS = "LOGIN_SUCCESS";
    public static final String PASSWORD_INCORRECT = "Password incorrect. Please try again.";
    public static final String USER_NOT_EXIST = "User does not exist. Please try again.";
    public static final String ALREADY_LOGIN = "User already logged in. Please try again.";

    public SystemReply(MessageContent content) {
        super(content);
    }

    @Override
    public String getType() {
        return "SYSTEM_REPLY";
    }
}
```
- **第142-158行**: 系统回复消息类
  - 继承SystemMessage基类
  - 定义了常用的系统回复常量：
    - `LOGIN_SUCCESS`: 登录成功标识
    - `PASSWORD_INCORRECT`: 密码错误提示
    - `USER_NOT_EXIST`: 用户不存在提示
    - `ALREADY_LOGIN`: 用户已登录提示
  - `getType()`: 返回"SYSTEM_REPLY"类型标识

### SystemRequest 系统请求消息

```java
public static class SystemRequest extends SystemMessage {
    private static final long serialVersionUID = 1L;
    private String username;

    public SystemRequest(String username, MessageContent content) {
        super(content);
        this.username = username;
    }

    public String getUsername() {
        return username;
    }

    @Override
    public String getType() {
        return "SYSTEM_REQUEST";
    }
}
```
- **第161-178行**: 系统请求消息类
  - 继承SystemMessage基类
  - `username`: 请求发起者的用户名
  - 用于客户端向服务器发送系统请求
  - `getUsername()`: 获取请求发起者用户名
  - `getType()`: 返回"SYSTEM_REQUEST"类型标识

### SystemBroadcast 系统广播消息

```java
public static class SystemBroadcast extends SystemMessage {
    private static final long serialVersionUID = 1L;
    private String type;
    private String username;

    public SystemBroadcast(MessageContent content, String type, String username) {
        super(content);
        this.type = type;
        this.username = username;
    }

    public String getBroadcastType() {
        return type;
    }

    public String getUsername() {
        return username;
    }

    @Override
    public String getType() {
        return "SYSTEM_BROADCAST";
    }
}
```
- **第181-204行**: 系统广播消息类
  - 继承SystemMessage基类
  - `type`: 广播类型（如"join"、"left"）
  - `username`: 相关的用户名
  - 用于服务器广播用户加入/离开等系统事件
  - `getBroadcastType()`: 获取广播类型
  - `getUsername()`: 获取相关用户名
  - `getType()`: 返回"SYSTEM_BROADCAST"类型标识

### SystemUserList 系统用户列表消息

```java
public static class SystemUserList extends SystemMessage {
    private static final long serialVersionUID = 1L;
    private List<String> users;

    public SystemUserList(MessageContent content, List<String> users) {
        super(content);
        this.users = users;
    }

    public List<String> getUsers() {
        return users;
    }

    @Override
    public String getType() {
        return "SYSTEM_USER_LIST";
    }
}
```
- **第207-224行**: 系统用户列表消息类
  - 继承SystemMessage基类
  - `users`: 用户名列表
  - 用于服务器向客户端发送在线用户列表
  - `getUsers()`: 获取用户列表
  - `getType()`: 返回"SYSTEM_USER_LIST"类型标识

## Messages 消息工具类

### 消息前缀生成方法

```java
public static class Messages {
    public static String getMessagePrefix(Message message, String currentUsername) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        String timestamp = LocalDateTime.now().format(formatter);

        if (message instanceof UserBroadcastMessage) {
            UserBroadcastMessage ubm = (UserBroadcastMessage) message;
            String sender = ubm.isAnonymous() ? "匿名用户" : ubm.getSender();
            return String.format("[%s] %s", timestamp, sender);
        } else if (message instanceof UserPrivateMessage) {
            UserPrivateMessage upm = (UserPrivateMessage) message;
            String sender = upm.isAnonymous() ? "匿名用户" : upm.getSender();
            if (Objects.equals(upm.getSender(), currentUsername)) {
                return String.format("[%s] 你 -> %s (私聊)", timestamp, upm.getReceiver());
            } else {
                return String.format("[%s] %s -> 你 (私聊)", timestamp, sender);
            }
        } else if (message instanceof SystemMessage) {
            return String.format("[%s] 系统", timestamp);
        }
        return String.format("[%s] 未知", timestamp);
    }
```
- **第227-248行**: 消息前缀生成方法
  - `formatter`: 时间格式化器，格式为"时:分:秒"
  - `timestamp`: 当前时间戳
  - **UserBroadcastMessage处理**:
    - 如果是匿名消息，显示"匿名用户"
    - 否则显示实际发送者用户名
    - 格式：`[时间] 发送者`
  - **UserPrivateMessage处理**:
    - 如果发送者是当前用户，显示"你 -> 接收者 (私聊)"
    - 如果发送者是其他用户，显示"发送者 -> 你 (私聊)"
    - 匿名私聊显示"匿名用户"
  - **SystemMessage处理**:
    - 显示"系统"作为发送者
  - **其他消息类型**:
    - 显示"未知"作为发送者

### 消息内容获取方法

```java
    public static String getMessageContent(Message message) {
        return message.getContent().getContent().toString();
    }
}
```
- **第250-253行**: 消息内容获取方法
  - 从Message对象中提取实际的文本内容
  - 调用`getContent().getContent().toString()`获取字符串形式的内容

## 消息类体系设计分析

### 继承层次结构

```
Message (抽象基类)
├── UserMessage (用户消息基类)
│   ├── UserBroadcastMessage (群聊消息)
│   └── UserPrivateMessage (私聊消息)
└── SystemMessage (系统消息基类)
    ├── SystemReply (系统回复)
    ├── SystemRequest (系统请求)
    ├── SystemBroadcast (系统广播)
    └── SystemUserList (用户列表)
```

### 设计特点

1. **类型安全**: 每个消息类型都有明确的类定义，避免类型混淆
2. **可扩展性**: 通过继承可以轻松添加新的消息类型
3. **序列化支持**: 所有类都实现Serializable接口，支持网络传输
4. **类型标识**: 每个具体类都实现getType()方法，提供类型标识字符串
5. **数据封装**: 使用private/protected成员变量和public访问方法

### 消息流向

1. **客户端到服务器**:
   - `UserBroadcastMessage`: 群聊消息
   - `UserPrivateMessage`: 私聊消息
   - `SystemRequest`: 系统请求（如登录、命令）

2. **服务器到客户端**:
   - `SystemReply`: 系统回复（如登录结果）
   - `SystemBroadcast`: 系统广播（如用户加入/离开）
   - `SystemUserList`: 在线用户列表
   - `UserBroadcastMessage`: 转发的群聊消息
   - `UserPrivateMessage`: 转发的私聊消息

### 匿名功能实现

- `UserMessage`基类包含`isAnonymous`字段
- 在消息显示时，通过`Messages.getMessagePrefix()`方法处理匿名显示
- 匿名用户显示为"匿名用户"而不是真实用户名

## 总结

ChatRoomShared.java文件是整个聊天室系统的核心通信协议定义：

1. **Constants类**: 定义了系统常量，便于统一管理配置
2. **NetworkUtil类**: 提供网络工具方法，如IP地址获取
3. **消息类体系**: 定义了完整的消息类型层次结构，支持各种通信需求
4. **Messages工具类**: 提供消息格式化和内容提取的工具方法

这个设计确保了客户端和服务器之间的通信协议一致性，支持群聊、私聊、匿名聊天、系统通知等功能，具有良好的可扩展性和维护性。所有消息类都支持序列化，可以通过网络安全传输，是整个聊天室系统的通信基础。
