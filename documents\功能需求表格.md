# 简易聊天室系统功能需求

## 服务器端功能需求

服务端功能需求包括用户加载功能、命令功能、端口侦听功能、用户登录验证功能、聊天功能、匿名功能和日志功能，具体解释见表1-1。

### 表1-1 服务器端功能需求

| 功能模块 | 功能描述 | 具体要求 | 实现方式 |
|---------|---------|---------|---------|
| **用户加载功能** | 启动时读取用户文件，加载所有用户信息 | 1. 读入用户文件（至少10个用户）<br>2. 用户名和密码明文保存<br>3. 支持用户信息的内存管理 | 从users.txt文件读取用户名:密码格式的数据，存储到HashMap中进行管理 |
| **命令功能** | 提供服务器管理命令接口 | 1. list：列出全部在线用户<br>2. listall：列出全部用户<br>3. quit：退出系统<br>4. 显示命令提示符 | 通过服务器管理界面接收命令输入，实现相应的管理功能 |
| **端口侦听功能** | 在固定端口监听客户端连接 | 1. 使用8000以上的固定端口<br>2. 持续监听客户端连接请求<br>3. 支持多客户端并发连接 | 使用ServerSocket在8888端口监听，通过线程池处理多客户端连接 |
| **用户登录验证功能** | 验证客户端用户身份 | 1. 接收用户名和密码<br>2. 验证用户信息正确性<br>3. 发送验证结果给客户端<br>4. 支持重复验证直到成功或断开 | 通过UserManager进行身份验证，发送SystemReply消息返回验证结果 |
| **聊天功能** | 转发和管理聊天消息 | 1. 至少支持5个客户端同时聊天<br>2. 转发群聊消息给所有在线用户<br>3. 转发私聊消息给指定用户<br>4. 维护在线用户列表 | 使用多线程处理客户端消息，通过消息类型判断进行广播或定向转发 |
| **匿名功能** | 支持匿名聊天模式 | 1. 支持用户匿名发送消息<br>2. 隐藏真实用户名显示<br>3. 保持消息转发功能完整性 | 通过消息对象的isAnonymous字段控制，在消息显示时替换为"匿名用户" |
| **日志功能** | 记录用户行为和系统事件 | 1. 记录登录成功信息（IP地址、时间）<br>2. 记录登录失败信息（IP地址、时间）<br>3. 记录退出聊天室时间<br>4. 使用文本文件存储日志 | 通过Logger类记录到chat.log文件，包含时间戳、IP地址、用户行为等信息 |

## 客户端功能需求

客户端功能需求包括连接服务器功能、登录功能、消息展示功能、用户输入判定功能和命令功能，具体解释见表1-2。

### 表1-2 客户端功能需求

| 功能模块 | 功能描述 | 具体要求 | 实现方式 |
|---------|---------|---------|---------|
| **连接服务器功能** | 建立与服务器的网络连接 | 1. 启动后首先连接到服务器端<br>2. 支持服务器地址和端口配置<br>3. 提供连接测试功能<br>4. 处理连接失败情况 | 通过Socket建立TCP连接，使用ObjectInputStream/ObjectOutputStream进行通信 |
| **登录功能** | 用户身份验证和登录流程 | 1. 提示用户输入用户名和密码<br>2. 发送验证信息给服务器<br>3. 处理验证结果<br>4. 验证失败时重新输入<br>5. 支持用户主动结束程序 | 通过登录界面收集用户信息，发送SystemRequest进行验证，根据SystemReply结果处理 |
| **消息展示功能** | 显示聊天内容和用户信息 | 1. 进入聊天室后显示提示符<br>2. 显示所有用户的非私聊信息<br>3. 显示发给自己的私聊信息<br>4. 支持富文本显示和消息格式化 | 使用JTextPane显示聊天内容，通过消息类型判断显示方式，支持时间戳和发送者信息 |
| **用户输入判定功能** | 识别和处理不同类型的用户输入 | 1. 普通字符串：广播类聊天信息<br>2. @+用户名：私聊信息<br>3. @@+命令：系统命令<br>4. 输入验证和错误处理 | 通过字符串前缀判断输入类型，创建相应的消息对象发送给服务器 |
| **命令功能** | 执行客户端系统命令 | 1. list：列出当前在线用户<br>2. quit：退出系统<br>3. showanonymous：显示当前聊天方式<br>4. anonymous：切换聊天方式（匿名/实名） | 通过命令解析器处理@@开头的命令，执行相应的客户端操作或发送系统请求 |

## 功能需求总结

### 核心技术要求
1. **图形界面**：基于Swing的GUI设计
2. **C/S架构**：客户端-服务器分离式架构
3. **网络通信**：TCP Socket + 对象序列化
4. **并发处理**：多线程支持多客户端
5. **数据持久化**：用户文件和日志文件

### 性能要求
1. **并发支持**：至少支持5个客户端同时在线
2. **实时性**：消息实时转发和显示
3. **稳定性**：异常处理和错误恢复
4. **可用性**：友好的用户界面和操作体验

### 安全要求
1. **身份验证**：用户名密码验证机制
2. **访问控制**：防止重复登录
3. **日志审计**：完整的用户行为记录
4. **数据完整性**：消息传输的可靠性

### 扩展性要求
1. **用户管理**：支持用户信息的增删改查
2. **功能扩展**：支持新命令和消息类型
3. **界面定制**：支持主题和样式配置
4. **协议扩展**：支持新的通信协议特性
